"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/colleges/page",{

/***/ "(app-pages-browser)/./src/app/colleges/page.js":
/*!**********************************!*\
  !*** ./src/app/colleges/page.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CollegesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Grid,List,Loader!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/CollegeCard */ \"(app-pages-browser)/./src/components/CollegeCard.js\");\n/* harmony import */ var _components_SearchFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/SearchFilters */ \"(app-pages-browser)/./src/components/SearchFilters.js\");\n/* harmony import */ var _lib_collegeData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/collegeData */ \"(app-pages-browser)/./src/lib/collegeData.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CollegesPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [colleges, setColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredColleges, setFilteredColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    // Search and filter states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ranking\");\n    // Initialize search params safely\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (searchParams) {\n            setSearchQuery(searchParams.get(\"search\") || \"\");\n            setSortBy(searchParams.get(\"sort\") || \"ranking\");\n        }\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load all colleges\n        const allColleges = (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_5__.getAllColleges)();\n        setColleges(allColleges);\n        setLoading(false);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Apply search, filters, and sorting\n        let result = colleges;\n        // Apply search\n        if (searchQuery) {\n            result = (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_5__.searchColleges)(searchQuery);\n        }\n        // Apply filters\n        if (Object.keys(filters).length > 0) {\n            result = (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_5__.filterColleges)(filters);\n            if (searchQuery) {\n                // If both search and filters are applied, intersect the results\n                const searchResults = (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_5__.searchColleges)(searchQuery);\n                result = result.filter((college)=>searchResults.some((searchCollege)=>searchCollege.id === college.id));\n            }\n        }\n        // Apply sorting\n        result = (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_5__.sortColleges)(result, sortBy);\n        setFilteredColleges(result);\n    }, [\n        colleges,\n        searchQuery,\n        filters,\n        sortBy\n    ]);\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n    };\n    const handleFilter = (newFilters)=>{\n        setFilters(newFilters);\n    };\n    const handleSort = (newSortBy)=>{\n        setSortBy(newSortBy);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading colleges...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Engineering Colleges in Bangalore\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: [\n                                    \"Discover and compare \",\n                                    colleges.length,\n                                    \" top engineering colleges with detailed information about placements, courses, and facilities.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-max py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        onSearch: handleSearch,\n                        onFilter: handleFilter,\n                        onSort: handleSort,\n                        searchQuery: searchQuery,\n                        filters: filters,\n                        sortBy: sortBy\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: [\n                                            filteredColleges.length,\n                                            \" College\",\n                                            filteredColleges.length !== 1 ? \"s\" : \"\",\n                                            \" Found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            'Results for \"',\n                                            searchQuery,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: \"p-2 rounded-md transition-colors duration-200 \".concat(viewMode === \"grid\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                        \"aria-label\": \"Grid view\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"list\"),\n                                        className: \"p-2 rounded-md transition-colors duration-200 \".concat(viewMode === \"list\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                        \"aria-label\": \"List view\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    filteredColleges.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid_List_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"No colleges found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Try adjusting your search criteria or filters to find more results.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSearchQuery(\"\");\n                                    setFilters({});\n                                    setSortBy(\"ranking\");\n                                },\n                                className: \"btn-primary\",\n                                children: \"Clear All Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: viewMode === \"grid\" ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\" : \"space-y-6\",\n                        children: filteredColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                college: college,\n                                isCompact: viewMode === \"list\",\n                                showCompareButton: true\n                            }, college.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    filteredColleges.length > 0 && filteredColleges.length >= 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn-secondary\",\n                            children: \"Load More Colleges\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-600 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: filteredColleges.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-100\",\n                                        children: \"Colleges Available\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: [\n                                            Math.round(filteredColleges.reduce((sum, college)=>sum + college.placementRate, 0) / Math.max(filteredColleges.length, 1)),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-100\",\n                                        children: \"Average Placement Rate\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold mb-2\",\n                                        children: [\n                                            \"₹\",\n                                            Math.max(...filteredColleges.map((c)=>c.highestPackage), 0),\n                                            \" LPA\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-100\",\n                                        children: \"Highest Package\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\colleges\\\\page.js\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(CollegesPage, \"QwanGp6NimVQNlfDoE8TuODAcNk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = CollegesPage;\nvar _c;\n$RefreshReg$(_c, \"CollegesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/colleges/page.js\n"));

/***/ })

});