"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/compare/page",{

/***/ "(app-pages-browser)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: function() { return /* binding */ colleges; },\n/* harmony export */   filterColleges: function() { return /* binding */ filterColleges; },\n/* harmony export */   formatCampusSize: function() { return /* binding */ formatCampusSize; },\n/* harmony export */   formatCurrency: function() { return /* binding */ formatCurrency; },\n/* harmony export */   formatNIRF: function() { return /* binding */ formatNIRF; },\n/* harmony export */   getAggregateStats: function() { return /* binding */ getAggregateStats; },\n/* harmony export */   getAllColleges: function() { return /* binding */ getAllColleges; },\n/* harmony export */   getCollegeById: function() { return /* binding */ getCollegeById; },\n/* harmony export */   getFeaturedColleges: function() { return /* binding */ getFeaturedColleges; },\n/* harmony export */   getPlacementStats: function() { return /* binding */ getPlacementStats; },\n/* harmony export */   getTopCompanies: function() { return /* binding */ getTopCompanies; },\n/* harmony export */   getWhatsAppLink: function() { return /* binding */ getWhatsAppLink; },\n/* harmony export */   searchColleges: function() { return /* binding */ searchColleges; },\n/* harmony export */   sortColleges: function() { return /* binding */ sortColleges; }\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if ( true && !collegesData) {\n        try {\n            const response = await fetch(\"/colleges.json\");\n            collegesData = await response.json();\n        } catch (error) {\n            console.error(\"Failed to load college data:\", error);\n            collegesData = getFallbackData();\n        }\n    }\n    return collegesData || getFallbackData();\n};\n// Complete dataset from college.json - All 50 colleges\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"MS Ramaiah Institute of Technology\",\n            \"acronym\": \"MSRIT\",\n            \"ranking\": 6,\n            \"address\": \"MS Ramaiah Institute of Technology, MSR Nagar, MSRIT Post, Bengaluru, Karnataka - 560054.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0306\\xb0 N, 77.5653\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, , Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Integrated Manufacturing, Computer Science and Engineering, Digital Communication, Digital Electronics and Communication, Industrial Engineering, Manufacturing Science and Engineering, Software Engineering, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: B.Arch: Architecture; MBA: General; MCA: General; Ph.D.: All engineering disciplines, Architecture, Management.\",\n            \"placementDetails\": \"95% placement rate; companies include Amazon, Capgemini, Intel, TCS; highest package ~₹50 LPA (2023). Excellent tech and core placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 50,\n            \"infrastructure\": \"25-acre campus with smart classrooms, central library, hostels, sports complex (cricket, volleyball), gym, auditorium, and dining halls.\",\n            \"labs\": \"Advanced labs for AI, VLSI, Structural Engineering, Biotech, and Mechanical Engineering, equipped for research and industry projects.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve MSR Nagar; Sandal Soap Factory Metro Station (Green Line), ~2 km, accessible by auto or walk.\",\n            \"summary\": \"MSRIT, founded in 1962 by Dr. M.S. Ramaiah, spans 25 acres in North Bangalore. It offers 12 B.E. programs, 12 M.Tech specializations, and strong placements at firms like Amazon and Intel, peaking at ₹50 LPA in 2023. Labs support cutting-edge research, and connectivity includes BMTC buses and the Green Line metro.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1962,\n            \"nirf\": \"78\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"Sir M Visvesvaraya Institute of Technology\",\n            \"acronym\": \"Sir MVIT\",\n            \"ranking\": 7,\n            \"address\": \"Sir MVIT, Krishnadevaraya Nagar, Hunasamaranahalli, International Airport Road, Bengaluru, Karnataka - 562157.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1507\\xb0 N, 77.6082\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Computer science and engineering (cyber security and IoT) Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Integrated Manufacturing, Electronics, Mechanical Engineering (Design) - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines, Management.\",\n            \"placementDetails\": \"80%+ placement rate; companies include TCS, Wipro, Cognizant, Infosys; highest package ~₹30 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"133-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, auditorium, and canteens.\",\n            \"labs\": \"Labs for Electronics, Mechanical, Biotech, and Computer Science, supporting practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Airport Road; no direct metro, ~20 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Sir MVIT, established in 1986, spans a vast 133-acre campus on International Airport Road. It offers eight B.E. programs and reliable placements at firms like TCS and Wipro, peaking at ₹30 LPA in 2023. While its rural location limits accessibility, the large campus provides ample space for expansion.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SirMVIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"133 acres\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"Bangalore Institute of Technology\",\n            \"acronym\": \"BIT\",\n            \"ranking\": 8,\n            \"address\": \"BIT, K.R. Road, V.V. Puram, Bengaluru, Karnataka - 560004.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9561\\xb0 N, 77.5762\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Dell, Accenture, TCS; highest package ~₹37 LPA (2023). Strong IT and core engineering focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 37,\n            \"infrastructure\": \"5-acre urban campus with classrooms, library, hostels, sports area (volleyball, badminton), auditorium, and canteens.\",\n            \"labs\": \"Labs for AI, VLSI, Civil, Mechanical, and Electronics, equipped for practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent K.R. Road; Chickpet Metro Station (Green Line), ~1.5 km, highly accessible.\",\n            \"summary\": \"BIT, founded in 1979 under the Vokkaligara Sangha, is a well-regarded VTU-affiliated college in central Bangalore. Its compact 5-acre campus hosts nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Placements are strong, with 85% of students placed in 2023 at firms like Infosys and Dell, peaking at ₹37 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Nitte Meenakshi Institute of Technology\",\n            \"acronym\": \"NMIT\",\n            \"ranking\": 9,\n            \"address\": \"NMIT, P.B. No. 6429, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Artificial engineering and machine learning, Artificial learning and data science, Computer Science and Engineering, Computer Science and Engineering (Business system), Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Data Sciences, Machine Design, Renewable Energy, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Microsoft, Infosys, Huawei, TCS; highest package ~₹40 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"23-acre campus with modern classrooms, library, hostels, sports facilities (cricket, tennis), gym, and dining halls.\",\n            \"labs\": \"Robotics Lab, Aerospace Lab, Data Science Lab, VLSI Lab, and Mechanical Workshop, supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve Yelahanka; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"NMIT, established in 2001 by the Nitte Education Trust, is an autonomous VTU-affiliated college in Yelahanka. Spanning 23 acres, it offers seven B.E. programs, six M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it emphasizes innovation and research, with over 50 patents filed.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NMIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"23 acres\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"PES University (Electronic City Campus)\",\n            \"acronym\": \"PESUECC\",\n            \"ranking\": 10,\n            \"address\": \"PES University, Electronic City Campus, Hosur Road, Electronic City, Bengaluru, Karnataka - 560100.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Artificial engineering and machine learning,  Electronics and Communication Engineering, Mechanical Engineering - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Automotive Engineering); MBA: Finance, Marketing, HR; MCA: General - **Other Programs**: Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Amazon, Intel, Flipkart, TCS; highest package ~₹60 LPA (2023). Excellent tech placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 60,\n            \"infrastructure\": \"50-acre campus with advanced classrooms, library, hostels, sports complex (football, basketball), gym, and cafeterias.\",\n            \"labs\": \"Labs for AI, Electronics, Automotive Engineering, and Software Development, equipped with industry-grade tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Electronic City Metro Station (upcoming Yellow Line, ~2 km), currently reliant on buses or autos.\",\n            \"summary\": \"PES University's Electronic City Campus, established in 2005, offers three B.Tech programs, three M.Tech specializations, MBA, MCA, and Ph.D. courses. Proximity to Electronic City enhances tech exposure. Ranked alongside its Ring Road counterpart in NIRF 2024 (101-150, University), this campus benefits from PES's legacy and alumni like Nishanth Ananthram (Google).\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESUECC\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2005,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"CMR Institute of Technology\",\n            \"acronym\": \"CMRIT\",\n            \"ranking\": 11,\n            \"address\": \"CMRIT, 132, AECS Layout, ITPL Main Road, Kundalahalli, Bengaluru, Karnataka - 560037.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Capgemini, IBM, Infosys; highest package ~₹30 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"8-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and VLSI, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on ITPL Road; Kundalahalli Metro Station (Purple Line), ~2 km, easily accessible.\",\n            \"summary\": \"CMRIT, founded in 2000 by the CMR Jnanadhara Trust, is a VTU-affiliated autonomous college in East Bangalore. Its compact 8-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it focuses on industry readiness.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=CMRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"8 acres\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"Dayananda Sagar College of Engineering\",\n            \"acronym\": \"DSCE\",\n            \"ranking\": 12,\n            \"address\": \"DSCE, Shavige Malleshwara Hills, Kumaraswamy Layout, Bengaluru, Karnataka - 560078.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Artificial engineering and machine learning, Computer science and engineering (data science), Computer science and engineering (cyber security), Computer science and engineering (Internet of things and block chain technology), Computer science and Business system, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Instrumentation Technology, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bioinformatics, Computer Integrated Manufacturing, Computer Science and Engineering, Design Engineering, Digital Electronics and Communication, Highway Technology, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Accenture, Cognizant, L&T, Infosys; highest package ~₹40 LPA (2023). Balanced IT and core placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"29-acre campus with modern classrooms, library, hostels, sports arena (cricket, basketball), gym, and dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Civil, VLSI, and Mechanical, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"DSCE, established in 1979, is a leading VTU-affiliated autonomous institute in South Bangalore. Its expansive 29-acre campus offers 14 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it boasts modern infrastructure and robust placements at firms like Accenture and L&T, peaking at ₹40 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"29 acres\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"BMS Institute of Technology\",\n            \"acronym\": \"BMSIT\",\n            \"ranking\": 13,\n            \"address\": \"BMSIT, Doddaballapur Main Road, Avalahalli, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1351\\xb0 N, 77.5718\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Computer science and business system, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Wipro, Dell, TCS; highest package ~₹35 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"21-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, and canteens.\",\n            \"labs\": \"Labs for AI, IoT, Electronics, and Mechanical, equipped for practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Doddaballapur Road; Yelahanka Metro Station (Green Line), ~6 km, requiring auto or bus.\",\n            \"summary\": \"BMSIT, established in 2002 as a sister institution to BMSCE, offers eight B.E. programs, two M.Tech specializations, MCA, and Ph.D. courses. Autonomous since 2016, it aligns with industry trends and offers a balanced environment with green spaces.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"21 acres\"\n        },\n        {\n            \"id\": 14,\n            \"name\": \"Reva University\",\n            \"acronym\": \"REVA\",\n            \"ranking\": 14,\n            \"address\": \"Reva University, Rukmini Knowledge Park, Kattigenahalli, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1167\\xb0 N, 77.6344\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Civil Engineering, Computer Science and Engineering, Computer Science and IT, Computer Science and system, Artificial learning and data science, Computer science and engineering (IoT & cyber security including blockchain technology), CSE (AI & ML), CSE (Cybersecurity), Electronics and Communication Engineering, Electronics and Computer  Engineering,  Mechanical Engineering, Bioelectronics, Robotics and Automation - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design, Power Electronics, Structural Engineering - MBA: Finance, Marketing, HR - MCA: General - M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts\",\n            \"placementDetails\": \"80% placement rate; companies include Amazon, TCS, IBM, Infosys; highest package ~₹45 LPA (2023). Strong tech placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"45-acre campus with modern classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Robotics, Cloud Computing, Civil Engineering, and VLSI equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kattigenahalli Road; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Reva University, established in 2004 as Reva Institute of Technology and granted university status in 2012, is a private institution in Kattigenahalli, Yelahanka, North Bangalore. Its 45-acre campus offers eight B.Tech specializations, BBA, B.Arch, M.Tech, MBA, M.Des, and Ph.D. courses, enrolling over 15,000 students across disciplines. Reva emphasizes multidisciplinary education blending engineering with management and design, featuring programs like Cybersecurity and Robotics. Labs support innovation, and placements are excellent, with 80% of students placed at firms like Amazon and TCS, peaking at ₹45 LPA. Ranked 151-200 in NIRF 2024 (University), it focuses on employability and entrepreneurship.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=REVA\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2004,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"45 acres\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"MS Ramaiah University of Applied Sciences\",\n            \"acronym\": \"MSRUAS\",\n            \"ranking\": 15,\n            \"address\": \"MSRUAS, University House, Gnanagangothri Campus, New BEL Road, MSR Nagar, Bengaluru, Karnataka - 560054\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0309\\xb0 N, 77.5643\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Aerospace Engineering, Artificial engineering and machine learning , Information science and engineering, Mathematics and computing, Robotics, Automotive Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Mechanical Engineering - B.Des: Product Design, Fashion Design - BBA: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Aircraft Design, Automotive Electronics, Data Sciences, Structural Engineering - MBA: General - M.Des: Product Design - M.Pharm: Pharmaceutics - **Other Programs**: Ph.D.: Engineering, Design, Pharmacy, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, HCL, Bosch, TCS; highest package ~₹40 LPA (2023). Balanced tech and core placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"25-acre campus with advanced classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aerospace, Automotive, Data Sciences, and Pharmacy equipped for research and industry collaboration.\",\n            \"busAndMetroConvenience\": \"BMTC buses on New BEL Road; Sandal Soap Factory Metro Station (Green Line), ~2 km, easily accessible.\",\n            \"summary\": \"MS Ramaiah University of Applied Sciences (MSRUAS), established in 2013 under the Gokula Education Foundation, is located in MSR Nagar, North Bangalore. Its 25-acre campus offers seven B.Tech programs, B.Des, BBA, B.Pharm, M.Tech, MBA, M.Des, M.Pharm, and Ph.D. courses, serving over 5,000 students. MSRUAS emphasizes applied learning with niche programs like Aircraft Design and Automotive Engineering. Labs drive research, and placements are solid, with 85% of students placed at firms like Infosys and Bosch, peaking at ₹40 LPA. Connectivity is excellent, with BMTC buses and the Green Line metro station nearby.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRUAS\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 16,\n            \"name\": \"Siddaganga Institute of Technology\",\n            \"acronym\": \"SIT\",\n            \"ranking\": 16,\n            \"address\": \"SIT, B.H. Road, Tumakuru, Karnataka - 572103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.3409\\xb0 N, 77.1180\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹25 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Biotech, Chemical, Civil, and Electronics, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Tumakuru; no metro connectivity, ~70 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"SIT, established in 1963 by the Siddaganga Education Society, is located in Tumakuru, 70 km from Bangalore. Its sprawling 100-acre campus offers nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes discipline and values-based education. Placements are reliable, with 80% of students placed at firms like TCS and Infosys, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 17,\n            \"name\": \"JSS Science and Technology University\",\n            \"acronym\": \"JSSTU\",\n            \"ranking\": 17,\n            \"address\": \"JSSTU, JSS Technical Institutions Campus, Mysuru, Karnataka - 570006\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3375\\xb0 N, 76.6244\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.E.: Aeronautical Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics - B.Arch: Architecture - **Postgraduate**: M.Tech: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - MBA: General - M.Arch: Architecture - **Other Programs**: Ph.D.: Engineering, Architecture, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, TCS, Wipro, L&T; highest package ~₹30 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"200-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Chemical, and Electronics, equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses within Mysuru; no metro connectivity, ~150 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"JSS Science and Technology University (JSSTU), established in 1963 and granted university status in 2008, is located in Mysuru. Its expansive 200-acre campus offers 11 B.E. programs, B.Arch, M.Tech, MBA, M.Arch, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes research and innovation. Placements are strong, with 85% of students placed at firms like Infosys and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=JSSTU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"200 acres\"\n        },\n        {\n            \"id\": 18,\n            \"name\": \"Sapthagiri College of Engineering\",\n            \"acronym\": \"SCE\",\n            \"ranking\": 18,\n            \"address\": \"SCE, 14/5, Chikkasandra, Hesaraghatta Main Road, Bengaluru, Karnataka - 560057\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0833\\xb0 N, 77.5167\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹20 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"15-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; no direct metro, ~15 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"SCE, established in 1999 by the Sapthagiri Educational Trust, is located in Chikkasandra, North Bangalore. Its 15-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it focuses on affordable quality education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1999,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"15 acres\"\n        },\n        {\n            \"id\": 19,\n            \"name\": \"Atria Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 19,\n            \"address\": \"AIT, Anandnagar, Hebbal, Bengaluru, Karnataka - 560024\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0358\\xb0 N, 77.5970\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, Accenture; highest package ~₹25 LPA (2023). Solid IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, badminton), gym, canteens.\",\n            \"labs\": \"Labs for CSE, VLSI, Electronics, and Mechanical, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hebbal Road; Hebbal Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"AIT, established in 2000 by the Atria Educational Trust, is located in Anandnagar, Hebbal, North Bangalore. Its compact 10-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2016, it emphasizes industry readiness. Placements are good, with 80% of students placed at firms like Infosys and TCS, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 20,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT_Acharya\",\n            \"ranking\": 20,\n            \"address\": \"Acharya Institute of Technology, Acharya Dr. Sarvepalli Radhakrishnan Road, Soldevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1000\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹22 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, CSE, ECE, and Mechanical, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soldevanahalli Road; no direct metro, ~10 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Acharya Institute of Technology, established in 2000 by the Acharya Institutes, is located in Soldevanahalli, North Bangalore. Its sprawling 120-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it emphasizes holistic education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT_Acharya\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 21,\n            \"name\": \"New Horizon College of Engineering\",\n            \"acronym\": \"NHCE\",\n            \"ranking\": 21,\n            \"address\": \"NHCE, Near Marathahalli, Outer Ring Road, Bengaluru, Karnataka - 560103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9591\\xb0 N, 77.7085\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, IBM; highest package ~₹28 LPA (2023). Good IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 28,\n            \"infrastructure\": \"62-acre campus with modern classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aeronautics, VLSI, CSE, and Electronics, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Outer Ring Road; Marathahalli Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"NHCE, established in 1982 by the New Horizon Educational Trust, is located near Marathahalli on the Outer Ring Road. Its 62-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2007, it emphasizes innovation and entrepreneurship. Placements are good, with 80% of students placed at firms like Infosys and IBM, peaking at ₹28 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NHCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1982,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"62 acres\"\n        },\n        {\n            \"id\": 22,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 22,\n            \"address\": \"Presidency University, Itgalpura, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2167\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering, Electrical and Electronics Engineering; BBA: General; B.Com: General; B.A.: Various specializations; B.Sc.: Various specializations; B.Des: Product Design; B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design; MBA: General; M.Com: General; M.A.: Various specializations; M.Sc.: Various specializations; M.Des: Product Design; M.Pharm: Pharmacy - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts, Pharmacy\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹18 LPA (2023). Entry-level placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Pharmacy, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; no direct metro, ~15 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Presidency University, established in 2013, is a private university located in Itgalpura, Rajanakunte, North Bangalore. Its expansive 100-acre campus offers five B.Tech programs, BBA, B.Com, B.A., B.Sc., B.Des, B.Pharm, M.Tech, MBA, M.Com, M.A., M.Sc., M.Des, M.Pharm, and Ph.D. courses across multiple disciplines. The university emphasizes multidisciplinary education and industry readiness. Placements are moderate, with 70% of students placed at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"100 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map((param)=>{\n        let [range, count] = param;\n        return {\n            range,\n            count\n        };\n    });\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b - a;\n    }).slice(0, 10).map((param)=>{\n        let [company, mentions] = param;\n        return {\n            company,\n            mentions\n        };\n    });\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return \"₹\".concat((amount / 100).toFixed(2), \" Cr\");\n    }\n    return \"₹\".concat(amount, \" LPA\");\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return \"\".concat(size, \" acres\");\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : \"NIRF \".concat(nirf);\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = function() {\n    let collegeName = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n    const message = collegeName ? \"Hi! I'm interested in learning more about \".concat(collegeName, \" and would like a free consultation.\") : \"Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?\";\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodeURIComponent(message));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/collegeData.js\n"));

/***/ })

});