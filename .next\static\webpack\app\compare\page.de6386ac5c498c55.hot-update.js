"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/compare/page",{

/***/ "(app-pages-browser)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: function() { return /* binding */ colleges; },\n/* harmony export */   filterColleges: function() { return /* binding */ filterColleges; },\n/* harmony export */   formatCampusSize: function() { return /* binding */ formatCampusSize; },\n/* harmony export */   formatCurrency: function() { return /* binding */ formatCurrency; },\n/* harmony export */   formatNIRF: function() { return /* binding */ formatNIRF; },\n/* harmony export */   getAggregateStats: function() { return /* binding */ getAggregateStats; },\n/* harmony export */   getAllColleges: function() { return /* binding */ getAllColleges; },\n/* harmony export */   getCollegeById: function() { return /* binding */ getCollegeById; },\n/* harmony export */   getFeaturedColleges: function() { return /* binding */ getFeaturedColleges; },\n/* harmony export */   getPlacementStats: function() { return /* binding */ getPlacementStats; },\n/* harmony export */   getTopCompanies: function() { return /* binding */ getTopCompanies; },\n/* harmony export */   getWhatsAppLink: function() { return /* binding */ getWhatsAppLink; },\n/* harmony export */   searchColleges: function() { return /* binding */ searchColleges; },\n/* harmony export */   sortColleges: function() { return /* binding */ sortColleges; }\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if ( true && !collegesData) {\n        try {\n            const response = await fetch(\"/colleges.json\");\n            collegesData = await response.json();\n        } catch (error) {\n            console.error(\"Failed to load college data:\", error);\n            collegesData = getFallbackData();\n        }\n    }\n    return collegesData || getFallbackData();\n};\n// Fallback data for SSR and error cases - Complete dataset from college.json\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map((param)=>{\n        let [range, count] = param;\n        return {\n            range,\n            count\n        };\n    });\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b - a;\n    }).slice(0, 10).map((param)=>{\n        let [company, mentions] = param;\n        return {\n            company,\n            mentions\n        };\n    });\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return \"₹\".concat((amount / 100).toFixed(2), \" Cr\");\n    }\n    return \"₹\".concat(amount, \" LPA\");\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return \"\".concat(size, \" acres\");\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : \"NIRF \".concat(nirf);\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = function() {\n    let collegeName = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n    const message = collegeName ? \"Hi! I'm interested in learning more about \".concat(collegeName, \" and would like a free consultation.\") : \"Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?\";\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodeURIComponent(message));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/collegeData.js\n"));

/***/ })

});