/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(rsc)/./src/app/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.js */ \"(ssr)/./src/components/Header.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDc3JjJTVDY29tcG9uZW50cyU1Q0hlYWRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3VzZXIlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDZWR1Y2F0aW9uJTIwMSU1Q3NyYyU1Q3N0eWxlcyU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBeUk7QUFDekkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iYW5nYWxvcmUtZW5naW5lZXJpbmctY29sbGVnZXMvPzIxNTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGVkdWNhdGlvbiAxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZWR1Y2F0aW9uIDFcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Cpage.js&server=true!":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Cpage.js&server=true! ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(ssr)/./src/app/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDc3JjJTVDYXBwJTVDcGFnZS5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iYW5nYWxvcmUtZW5naW5lZXJpbmctY29sbGVnZXMvPzE0NzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGVkdWNhdGlvbiAxXFxcXHNyY1xcXFxhcHBcXFxccGFnZS5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BookOpen,CheckCircle,MapPin,Shield,Star,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/CollegeCard */ \"(ssr)/./src/components/CollegeCard.js\");\n/* harmony import */ var _lib_collegeData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/collegeData */ \"(ssr)/./src/lib/collegeData.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HomePage() {\n    const [featuredColleges, setFeaturedColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [topCompanies, setTopCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFeaturedColleges((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getFeaturedColleges)());\n        setStats((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getAggregateStats)());\n        setTopCompanies((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_4__.getTopCompanies)());\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentSlide((prev)=>(prev + 1) % Math.max(1, featuredColleges.length - 2));\n        }, 5000);\n        return ()=>clearInterval(timer);\n    }, [\n        featuredColleges.length\n    ]);\n    const trustIndicators = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"1000+ Students Guided\",\n            color: \"text-blue-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Real-Time Placement Data\",\n            color: \"text-green-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: \"AI-Powered College Matcher\",\n            color: \"text-purple-600\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: \"Verified Information\",\n            color: \"text-orange-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"gradient-bg text-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold mb-6 animate-slide-up\",\n                                children: [\n                                    \"Your Gateway to Top Engineering Colleges in\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-300\",\n                                        children: \"Bangalore\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl mb-8 text-blue-100 animate-slide-up\",\n                                children: \"Compare placements, courses, and rankings. Let us guide you to the perfect fit!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://wa.me/************?text=Hi! I need personalized guidance for engineering colleges in Bangalore.\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Get Personalized Guidance (Free WhatsApp Consultation)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/colleges\",\n                                        className: \"bg-white hover:bg-gray-100 text-primary-600 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-xl flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Explore All Colleges\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Why Choose Our Platform?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Get access to comprehensive data and insights about Bangalore's top engineering colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 text-green-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: [\n                                                stats.avgPlacementRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Placement Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 text-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: \"₹1.15 Cr\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Highest Package\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-100 text-purple-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: stats.avgCampusSize\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Average Campus Size (Acres)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-100 text-orange-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-8 h-8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                            children: [\n                                                stats.totalColleges,\n                                                \"+\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Engineering Colleges\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: trustIndicators.map((indicator, index)=>{\n                                const Icon = indicator.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `w-8 h-8 ${indicator.color}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-gray-800\",\n                                            children: indicator.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gray-50 section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Top Engineering Colleges in Bangalore\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Discover the best engineering colleges with excellent placement records and world-class facilities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        featuredColleges.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: featuredColleges.slice(0, 6).map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollegeCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            college: college\n                                        }, college.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/colleges\",\n                                        className: \"btn-primary inline-flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"View All Colleges\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BookOpen_CheckCircle_MapPin_Shield_Star_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white section-padding\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Top Recruiting Companies\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600\",\n                                    children: \"Leading companies that regularly recruit from Bangalore engineering colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\",\n                            children: topCompanies.slice(0, 10).map((company, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 card card-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: company.company\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                company.mentions,\n                                                \" colleges\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\page.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/CollegeCard.js":
/*!***************************************!*\
  !*** ./src/components/CollegeCard.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollegeCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tram-front.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Calendar,ExternalLink,MapPin,Train,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_collegeData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/collegeData */ \"(ssr)/./src/lib/collegeData.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction CollegeCard({ college, showCompareButton = true, isCompact = false }) {\n    if (isCompact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card p-4 card-hover\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-primary-100 text-primary-600 px-2 py-1 rounded-full text-xs font-semibold\",\n                                    children: [\n                                        \"#\",\n                                        college.ranking\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 text-sm\",\n                                    children: college.acronym\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 12,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-secondary-600\",\n                                    children: [\n                                        college.placementRate,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Placement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(college.highestPackage),\n                                \" highest\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: `/colleges/${college.id}`,\n                            className: \"text-primary-600 hover:text-primary-700 text-sm font-medium\",\n                            children: \"View Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card p-6 card-hover\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-sm font-semibold\",\n                                children: [\n                                    \"Rank #\",\n                                    college.ranking\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            college.metroAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Metro\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.formatNIRF)(college.nirf)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-3 w-3 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Est. \",\n                                    college.establishedYear\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-gray-900 mb-1\",\n                        children: college.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-semibold text-primary-600\",\n                        children: college.acronym\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center p-3 bg-green-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: [\n                                    college.placementRate,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Placement Rate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center p-3 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(college.highestPackage)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Highest Package\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate\",\n                                children: college.campusSize\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Multiple Engineering Programs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 mr-2 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Strong Industry Connections\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: `/colleges/${college.id}`,\n                        className: \"flex-1 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 text-center flex items-center justify-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"View Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Calendar_ExternalLink_MapPin_Train_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    showCompareButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            // Add to comparison logic would go here\n                            alert(`Added ${college.acronym} to comparison`);\n                        },\n                        className: \"flex-1 bg-white hover:bg-gray-50 text-primary-600 font-semibold py-2 px-4 rounded-lg border-2 border-primary-600 transition-all duration-200 text-center\",\n                        children: \"Compare\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.getWhatsAppLink)(college.name),\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-center text-sm flex items-center justify-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Get Guidance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\CollegeCard.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CollegeCard.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"All Colleges\",\n            href: \"/colleges\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Compare\",\n            href: \"/compare\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Rankings\",\n            href: \"/colleges?sort=ranking\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-40 border-b border-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-max\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-primary-600 to-secondary-600 p-2 rounded-xl group-hover:scale-110 transition-transform duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Bangalore Engineering\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"College Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center space-x-2 text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4 group-hover:scale-110 transition-transform duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary text-sm\",\n                                children: \"Free Consultation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:text-primary-600 hover:bg-gray-100 transition-colors duration-200\",\n                            \"aria-label\": \"Toggle menu\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-100 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center space-x-3 text-gray-700 hover:text-primary-600 font-medium py-2 px-4 rounded-lg hover:bg-gray-50 transition-all duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary w-full text-center text-sm\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"Free Consultation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: () => (/* binding */ colleges),\n/* harmony export */   filterColleges: () => (/* binding */ filterColleges),\n/* harmony export */   formatCampusSize: () => (/* binding */ formatCampusSize),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNIRF: () => (/* binding */ formatNIRF),\n/* harmony export */   getAggregateStats: () => (/* binding */ getAggregateStats),\n/* harmony export */   getAllColleges: () => (/* binding */ getAllColleges),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getFeaturedColleges: () => (/* binding */ getFeaturedColleges),\n/* harmony export */   getPlacementStats: () => (/* binding */ getPlacementStats),\n/* harmony export */   getTopCompanies: () => (/* binding */ getTopCompanies),\n/* harmony export */   getWhatsAppLink: () => (/* binding */ getWhatsAppLink),\n/* harmony export */   searchColleges: () => (/* binding */ searchColleges),\n/* harmony export */   sortColleges: () => (/* binding */ sortColleges)\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if (false) {}\n    return collegesData || getFallbackData();\n};\n// Complete dataset from college.json - All 50 colleges\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"MS Ramaiah Institute of Technology\",\n            \"acronym\": \"MSRIT\",\n            \"ranking\": 6,\n            \"address\": \"MS Ramaiah Institute of Technology, MSR Nagar, MSRIT Post, Bengaluru, Karnataka - 560054.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0306\\xb0 N, 77.5653\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, , Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Integrated Manufacturing, Computer Science and Engineering, Digital Communication, Digital Electronics and Communication, Industrial Engineering, Manufacturing Science and Engineering, Software Engineering, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: B.Arch: Architecture; MBA: General; MCA: General; Ph.D.: All engineering disciplines, Architecture, Management.\",\n            \"placementDetails\": \"95% placement rate; companies include Amazon, Capgemini, Intel, TCS; highest package ~₹50 LPA (2023). Excellent tech and core placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 50,\n            \"infrastructure\": \"25-acre campus with smart classrooms, central library, hostels, sports complex (cricket, volleyball), gym, auditorium, and dining halls.\",\n            \"labs\": \"Advanced labs for AI, VLSI, Structural Engineering, Biotech, and Mechanical Engineering, equipped for research and industry projects.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve MSR Nagar; Sandal Soap Factory Metro Station (Green Line), ~2 km, accessible by auto or walk.\",\n            \"summary\": \"MSRIT, founded in 1962 by Dr. M.S. Ramaiah, spans 25 acres in North Bangalore. It offers 12 B.E. programs, 12 M.Tech specializations, and strong placements at firms like Amazon and Intel, peaking at ₹50 LPA in 2023. Labs support cutting-edge research, and connectivity includes BMTC buses and the Green Line metro.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1962,\n            \"nirf\": \"78\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"Sir M Visvesvaraya Institute of Technology\",\n            \"acronym\": \"Sir MVIT\",\n            \"ranking\": 7,\n            \"address\": \"Sir MVIT, Krishnadevaraya Nagar, Hunasamaranahalli, International Airport Road, Bengaluru, Karnataka - 562157.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1507\\xb0 N, 77.6082\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Computer science and engineering (cyber security and IoT) Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Integrated Manufacturing, Electronics, Mechanical Engineering (Design) - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines, Management.\",\n            \"placementDetails\": \"80%+ placement rate; companies include TCS, Wipro, Cognizant, Infosys; highest package ~₹30 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"133-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, auditorium, and canteens.\",\n            \"labs\": \"Labs for Electronics, Mechanical, Biotech, and Computer Science, supporting practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Airport Road; no direct metro, ~20 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Sir MVIT, established in 1986, spans a vast 133-acre campus on International Airport Road. It offers eight B.E. programs and reliable placements at firms like TCS and Wipro, peaking at ₹30 LPA in 2023. While its rural location limits accessibility, the large campus provides ample space for expansion.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SirMVIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"133 acres\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"Bangalore Institute of Technology\",\n            \"acronym\": \"BIT\",\n            \"ranking\": 8,\n            \"address\": \"BIT, K.R. Road, V.V. Puram, Bengaluru, Karnataka - 560004.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9561\\xb0 N, 77.5762\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Dell, Accenture, TCS; highest package ~₹37 LPA (2023). Strong IT and core engineering focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 37,\n            \"infrastructure\": \"5-acre urban campus with classrooms, library, hostels, sports area (volleyball, badminton), auditorium, and canteens.\",\n            \"labs\": \"Labs for AI, VLSI, Civil, Mechanical, and Electronics, equipped for practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent K.R. Road; Chickpet Metro Station (Green Line), ~1.5 km, highly accessible.\",\n            \"summary\": \"BIT, founded in 1979 under the Vokkaligara Sangha, is a well-regarded VTU-affiliated college in central Bangalore. Its compact 5-acre campus hosts nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Placements are strong, with 85% of students placed in 2023 at firms like Infosys and Dell, peaking at ₹37 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Nitte Meenakshi Institute of Technology\",\n            \"acronym\": \"NMIT\",\n            \"ranking\": 9,\n            \"address\": \"NMIT, P.B. No. 6429, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Artificial engineering and machine learning, Artificial learning and data science, Computer Science and Engineering, Computer Science and Engineering (Business system), Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Data Sciences, Machine Design, Renewable Energy, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Microsoft, Infosys, Huawei, TCS; highest package ~₹40 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"23-acre campus with modern classrooms, library, hostels, sports facilities (cricket, tennis), gym, and dining halls.\",\n            \"labs\": \"Robotics Lab, Aerospace Lab, Data Science Lab, VLSI Lab, and Mechanical Workshop, supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve Yelahanka; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"NMIT, established in 2001 by the Nitte Education Trust, is an autonomous VTU-affiliated college in Yelahanka. Spanning 23 acres, it offers seven B.E. programs, six M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it emphasizes innovation and research, with over 50 patents filed.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NMIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"23 acres\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"PES University (Electronic City Campus)\",\n            \"acronym\": \"PESUECC\",\n            \"ranking\": 10,\n            \"address\": \"PES University, Electronic City Campus, Hosur Road, Electronic City, Bengaluru, Karnataka - 560100.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Artificial engineering and machine learning,  Electronics and Communication Engineering, Mechanical Engineering - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Automotive Engineering); MBA: Finance, Marketing, HR; MCA: General - **Other Programs**: Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Amazon, Intel, Flipkart, TCS; highest package ~₹60 LPA (2023). Excellent tech placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 60,\n            \"infrastructure\": \"50-acre campus with advanced classrooms, library, hostels, sports complex (football, basketball), gym, and cafeterias.\",\n            \"labs\": \"Labs for AI, Electronics, Automotive Engineering, and Software Development, equipped with industry-grade tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Electronic City Metro Station (upcoming Yellow Line, ~2 km), currently reliant on buses or autos.\",\n            \"summary\": \"PES University's Electronic City Campus, established in 2005, offers three B.Tech programs, three M.Tech specializations, MBA, MCA, and Ph.D. courses. Proximity to Electronic City enhances tech exposure. Ranked alongside its Ring Road counterpart in NIRF 2024 (101-150, University), this campus benefits from PES's legacy and alumni like Nishanth Ananthram (Google).\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESUECC\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2005,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"CMR Institute of Technology\",\n            \"acronym\": \"CMRIT\",\n            \"ranking\": 11,\n            \"address\": \"CMRIT, 132, AECS Layout, ITPL Main Road, Kundalahalli, Bengaluru, Karnataka - 560037.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Capgemini, IBM, Infosys; highest package ~₹30 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"8-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and VLSI, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on ITPL Road; Kundalahalli Metro Station (Purple Line), ~2 km, easily accessible.\",\n            \"summary\": \"CMRIT, founded in 2000 by the CMR Jnanadhara Trust, is a VTU-affiliated autonomous college in East Bangalore. Its compact 8-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it focuses on industry readiness.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=CMRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"8 acres\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"Dayananda Sagar College of Engineering\",\n            \"acronym\": \"DSCE\",\n            \"ranking\": 12,\n            \"address\": \"DSCE, Shavige Malleshwara Hills, Kumaraswamy Layout, Bengaluru, Karnataka - 560078.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Artificial engineering and machine learning, Computer science and engineering (data science), Computer science and engineering (cyber security), Computer science and engineering (Internet of things and block chain technology), Computer science and Business system, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Instrumentation Technology, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bioinformatics, Computer Integrated Manufacturing, Computer Science and Engineering, Design Engineering, Digital Electronics and Communication, Highway Technology, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Accenture, Cognizant, L&T, Infosys; highest package ~₹40 LPA (2023). Balanced IT and core placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"29-acre campus with modern classrooms, library, hostels, sports arena (cricket, basketball), gym, and dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Civil, VLSI, and Mechanical, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"DSCE, established in 1979, is a leading VTU-affiliated autonomous institute in South Bangalore. Its expansive 29-acre campus offers 14 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it boasts modern infrastructure and robust placements at firms like Accenture and L&T, peaking at ₹40 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"29 acres\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"BMS Institute of Technology\",\n            \"acronym\": \"BMSIT\",\n            \"ranking\": 13,\n            \"address\": \"BMSIT, Doddaballapur Main Road, Avalahalli, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1351\\xb0 N, 77.5718\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Computer science and business system, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Wipro, Dell, TCS; highest package ~₹35 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"21-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, and canteens.\",\n            \"labs\": \"Labs for AI, IoT, Electronics, and Mechanical, equipped for practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Doddaballapur Road; Yelahanka Metro Station (Green Line), ~6 km, requiring auto or bus.\",\n            \"summary\": \"BMSIT, established in 2002 as a sister institution to BMSCE, offers eight B.E. programs, two M.Tech specializations, MCA, and Ph.D. courses. Autonomous since 2016, it aligns with industry trends and offers a balanced environment with green spaces.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"21 acres\"\n        },\n        {\n            \"id\": 14,\n            \"name\": \"Reva University\",\n            \"acronym\": \"REVA\",\n            \"ranking\": 14,\n            \"address\": \"Reva University, Rukmini Knowledge Park, Kattigenahalli, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1167\\xb0 N, 77.6344\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Civil Engineering, Computer Science and Engineering, Computer Science and IT, Computer Science and system, Artificial learning and data science, Computer science and engineering (IoT & cyber security including blockchain technology), CSE (AI & ML), CSE (Cybersecurity), Electronics and Communication Engineering, Electronics and Computer  Engineering,  Mechanical Engineering, Bioelectronics, Robotics and Automation - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design, Power Electronics, Structural Engineering - MBA: Finance, Marketing, HR - MCA: General - M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts\",\n            \"placementDetails\": \"80% placement rate; companies include Amazon, TCS, IBM, Infosys; highest package ~₹45 LPA (2023). Strong tech placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"45-acre campus with modern classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Robotics, Cloud Computing, Civil Engineering, and VLSI equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kattigenahalli Road; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Reva University, established in 2004 as Reva Institute of Technology and granted university status in 2012, is a private institution in Kattigenahalli, Yelahanka, North Bangalore. Its 45-acre campus offers eight B.Tech specializations, BBA, B.Arch, M.Tech, MBA, M.Des, and Ph.D. courses, enrolling over 15,000 students across disciplines. Reva emphasizes multidisciplinary education blending engineering with management and design, featuring programs like Cybersecurity and Robotics. Labs support innovation, and placements are excellent, with 80% of students placed at firms like Amazon and TCS, peaking at ₹45 LPA. Ranked 151-200 in NIRF 2024 (University), it focuses on employability and entrepreneurship.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=REVA\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2004,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"45 acres\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"MS Ramaiah University of Applied Sciences\",\n            \"acronym\": \"MSRUAS\",\n            \"ranking\": 15,\n            \"address\": \"MSRUAS, University House, Gnanagangothri Campus, New BEL Road, MSR Nagar, Bengaluru, Karnataka - 560054\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0309\\xb0 N, 77.5643\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Aerospace Engineering, Artificial engineering and machine learning , Information science and engineering, Mathematics and computing, Robotics, Automotive Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Mechanical Engineering - B.Des: Product Design, Fashion Design - BBA: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Aircraft Design, Automotive Electronics, Data Sciences, Structural Engineering - MBA: General - M.Des: Product Design - M.Pharm: Pharmaceutics - **Other Programs**: Ph.D.: Engineering, Design, Pharmacy, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, HCL, Bosch, TCS; highest package ~₹40 LPA (2023). Balanced tech and core placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"25-acre campus with advanced classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aerospace, Automotive, Data Sciences, and Pharmacy equipped for research and industry collaboration.\",\n            \"busAndMetroConvenience\": \"BMTC buses on New BEL Road; Sandal Soap Factory Metro Station (Green Line), ~2 km, easily accessible.\",\n            \"summary\": \"MS Ramaiah University of Applied Sciences (MSRUAS), established in 2013 under the Gokula Education Foundation, is located in MSR Nagar, North Bangalore. Its 25-acre campus offers seven B.Tech programs, B.Des, BBA, B.Pharm, M.Tech, MBA, M.Des, M.Pharm, and Ph.D. courses, serving over 5,000 students. MSRUAS emphasizes applied learning with niche programs like Aircraft Design and Automotive Engineering. Labs drive research, and placements are solid, with 85% of students placed at firms like Infosys and Bosch, peaking at ₹40 LPA. Connectivity is excellent, with BMTC buses and the Green Line metro station nearby.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRUAS\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 16,\n            \"name\": \"Siddaganga Institute of Technology\",\n            \"acronym\": \"SIT\",\n            \"ranking\": 16,\n            \"address\": \"SIT, B.H. Road, Tumakuru, Karnataka - 572103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.3409\\xb0 N, 77.1180\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹25 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Biotech, Chemical, Civil, and Electronics, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Tumakuru; no metro connectivity, ~70 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"SIT, established in 1963 by the Siddaganga Education Society, is located in Tumakuru, 70 km from Bangalore. Its sprawling 100-acre campus offers nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes discipline and values-based education. Placements are reliable, with 80% of students placed at firms like TCS and Infosys, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 17,\n            \"name\": \"JSS Science and Technology University\",\n            \"acronym\": \"JSSTU\",\n            \"ranking\": 17,\n            \"address\": \"JSSTU, JSS Technical Institutions Campus, Mysuru, Karnataka - 570006\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3375\\xb0 N, 76.6244\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.E.: Aeronautical Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics - B.Arch: Architecture - **Postgraduate**: M.Tech: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - MBA: General - M.Arch: Architecture - **Other Programs**: Ph.D.: Engineering, Architecture, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, TCS, Wipro, L&T; highest package ~₹30 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"200-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Chemical, and Electronics, equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses within Mysuru; no metro connectivity, ~150 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"JSS Science and Technology University (JSSTU), established in 1963 and granted university status in 2008, is located in Mysuru. Its expansive 200-acre campus offers 11 B.E. programs, B.Arch, M.Tech, MBA, M.Arch, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes research and innovation. Placements are strong, with 85% of students placed at firms like Infosys and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=JSSTU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"200 acres\"\n        },\n        {\n            \"id\": 18,\n            \"name\": \"Sapthagiri College of Engineering\",\n            \"acronym\": \"SCE\",\n            \"ranking\": 18,\n            \"address\": \"SCE, 14/5, Chikkasandra, Hesaraghatta Main Road, Bengaluru, Karnataka - 560057\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0833\\xb0 N, 77.5167\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹20 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"15-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; no direct metro, ~15 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"SCE, established in 1999 by the Sapthagiri Educational Trust, is located in Chikkasandra, North Bangalore. Its 15-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it focuses on affordable quality education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1999,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"15 acres\"\n        },\n        {\n            \"id\": 19,\n            \"name\": \"Atria Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 19,\n            \"address\": \"AIT, Anandnagar, Hebbal, Bengaluru, Karnataka - 560024\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0358\\xb0 N, 77.5970\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, Accenture; highest package ~₹25 LPA (2023). Solid IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, badminton), gym, canteens.\",\n            \"labs\": \"Labs for CSE, VLSI, Electronics, and Mechanical, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hebbal Road; Hebbal Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"AIT, established in 2000 by the Atria Educational Trust, is located in Anandnagar, Hebbal, North Bangalore. Its compact 10-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2016, it emphasizes industry readiness. Placements are good, with 80% of students placed at firms like Infosys and TCS, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 20,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT_Acharya\",\n            \"ranking\": 20,\n            \"address\": \"Acharya Institute of Technology, Acharya Dr. Sarvepalli Radhakrishnan Road, Soldevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1000\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹22 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, CSE, ECE, and Mechanical, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soldevanahalli Road; no direct metro, ~10 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Acharya Institute of Technology, established in 2000 by the Acharya Institutes, is located in Soldevanahalli, North Bangalore. Its sprawling 120-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it emphasizes holistic education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT_Acharya\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 21,\n            \"name\": \"New Horizon College of Engineering\",\n            \"acronym\": \"NHCE\",\n            \"ranking\": 21,\n            \"address\": \"NHCE, Near Marathahalli, Outer Ring Road, Bengaluru, Karnataka - 560103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9591\\xb0 N, 77.7085\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, IBM; highest package ~₹28 LPA (2023). Good IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 28,\n            \"infrastructure\": \"62-acre campus with modern classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aeronautics, VLSI, CSE, and Electronics, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Outer Ring Road; Marathahalli Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"NHCE, established in 1982 by the New Horizon Educational Trust, is located near Marathahalli on the Outer Ring Road. Its 62-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2007, it emphasizes innovation and entrepreneurship. Placements are good, with 80% of students placed at firms like Infosys and IBM, peaking at ₹28 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NHCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1982,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"62 acres\"\n        },\n        {\n            \"id\": 22,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 22,\n            \"address\": \"Presidency University, Itgalpura, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2167\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering, Electrical and Electronics Engineering; BBA: General; B.Com: General; B.A.: Various specializations; B.Sc.: Various specializations; B.Des: Product Design; B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design; MBA: General; M.Com: General; M.A.: Various specializations; M.Sc.: Various specializations; M.Des: Product Design; M.Pharm: Pharmacy - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts, Pharmacy\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹18 LPA (2023). Entry-level placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Pharmacy, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; no direct metro, ~15 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Presidency University, established in 2013, is a private university located in Itgalpura, Rajanakunte, North Bangalore. Its expansive 100-acre campus offers five B.Tech programs, BBA, B.Com, B.A., B.Sc., B.Des, B.Pharm, M.Tech, MBA, M.Com, M.A., M.Sc., M.Des, M.Pharm, and Ph.D. courses across multiple disciplines. The university emphasizes multidisciplinary education and industry readiness. Placements are moderate, with 70% of students placed at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 23,\n            \"name\": \"Dayananda Sagar Academy of Technology and Management\",\n            \"acronym\": \"DSATM\",\n            \"ranking\": 23,\n            \"address\": \"DSATM, Udayapura, Kanakapura Road, Bengaluru, Karnataka - 560082\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8756\\xb0 N, 77.5389\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: B.Arch: Architecture, MBA: General\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, L&T, Infosys, Wipro; highest package ~₹30 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for practical training with modest research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kanakapura Road; Konanakunte Cross Metro Station (Green Line), ~5 km, requiring auto or bus.\",\n            \"summary\": \"Dayananda Sagar Academy of Technology and Management (DSATM), established in 2011 by the Mahatma Gandhi Vidya Peetha Trust, is a VTU-affiliated autonomous college in Udayapura, South Bangalore. Its 10-acre campus offers six B.E. programs, two M.Tech specializations, B.Arch, and MBA courses, enrolling around 2,500 students. DSATM focuses on core and IT engineering alongside architecture, gaining autonomy in 2018. Placements are decent, with 80% of students placed in 2023 at firms like TCS and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSATM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2011,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 24,\n            \"name\": \"Dayananda Sagar University\",\n            \"acronym\": \"DSU\",\n            \"ranking\": 24,\n            \"address\": \"Devarakaggalahalli, Harohalli, Kanakapura Road, Ramanagara Dt., Bengaluru – 562 112\",\n            \"locationUrl\": \"https://www.google.co.in/maps/dir//Devarakaggalahalli,+Harohalli+Kanakapura+Road,+Dt,+Ramanagara,+Karnataka+562112/@12.6606565,77.368438,12z/data=!4m8!4m7!1m0!1m5!1m1!1s0x3bae5b32ad06ec57:0x95e7a57b8a6b94d2!2m2!1d77.4508399!2d12.6606692?entry=ttu&g_ep=EgoyMDI1MDMyMy4wIKXMDSoASAFQAw%3D%3D\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), Electronics and Communication Engineering, Mechanical Engineering, Artificial learning and data science, Computer science and engineering (data science), Robotics and artificial engineering, CSE&ME, Computer science and engineering (cyber security), Aerospace Engineering, Data Sciences - BBA: General - B.Com: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems - MBA: Finance, Marketing, HR - M.Pharm: Pharmacology - **Other Programs**: Ph.D.: Engineering, Management, Pharmacy\",\n            \"placementDetails\": \"85% placement rate; companies include Amazon, Infosys, Deloitte, TCS; highest package ~₹35 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"130-acre campus with advanced classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for AI, Biotech, Aerospace, and Pharmacy supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"Dayananda Sagar University (DSU), established in 2014 by the Mahatma Gandhi Vidya Peetha Trust, is a private university in Kumaraswamy Layout, South Bangalore. Its sprawling 130-acre campus offers six B.Tech programs, BBA, B.Com, B.Pharm, M.Tech, MBA, M.Pharm, and Ph.D. courses, serving over 5,000 students. DSU's modern curriculum includes Aerospace and Data Sciences, reflecting industry trends. Labs drive innovation, and placements are strong, with 85% of students placed in 2023 at firms like Amazon and Infosys, peaking at ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2014,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"130 acres\"\n        },\n        {\n            \"id\": 25,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 25,\n            \"address\": \"AIT, Acharya Dr. Sarvepalli Radhakrishnan Road, Soladevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0836\\xb0 N, 77.4819\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning,  Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Mechatronics, Mining Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Science and Engineering, Digital Communication Engineering, Machine Design - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹30 LPA (2023). Balanced placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Mechatronics, and Mining supporting research and training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soladevanahalli Road; Chikkabanavara Metro Station (upcoming Pink Line, ~5 km), currently reliant on buses.\",\n            \"summary\": \"Acharya Institute of Technology (AIT), established in 2000 by the Acharya Institutes Group, is a VTU-affiliated autonomous college in Soladevanahalli, North Bangalore. Its 120-acre campus offers 12 B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses, serving over 5,000 students. AIT focuses on diverse engineering fields like Aeronautical and Mining Engineering, gaining autonomy in 2017. Placements are strong, with 85% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 26,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 26,\n            \"address\": \"Presidency University, Itgalpur, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1717\\xb0 N, 77.6118\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (Artificial Intelligence & Machine Learning), Civil Engineering, Electronics and Communication Engineering, Mechanical Engineering, Petroleum Engineering, Data Science, Cybersecurity - BBA: General, Digital Marketing, Business Analytics - B.Com: General, Accounting and Taxation - B.Des: Product Design, Communication Design - B.A. LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems, Data Sciences - MBA: Finance, Marketing, Human Resources, Business Analytics - LLM: Intellectual Property Rights - **Other Programs**: Ph.D.: Engineering, Management, Law, Sciences\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, Capgemini, EY, TCS; highest package ~₹35 LPA (2023). Focus on IT and management roles.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"64-acre campus with modern classrooms, library, hostels (separate for boys and girls), sports complex (cricket, football, basketball), gym, auditorium, multi-cuisine cafeterias.\",\n            \"labs\": \"Advanced labs for AI, Civil Engineering, Electronics, Petroleum Engineering, and Cybersecurity equipped with industry-standard tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; Yelahanka Metro Station (Green Line), ~7 km, accessible by auto or college-provided transport; upcoming Pink Line will improve connectivity.\",\n            \"summary\": \"Presidency University, established in 2013 by the Presidency Group of Institutions, is a private university located in Itgalpur, Rajanakunte, near Yelahanka in North Bangalore. Spanning a 64-acre campus, it offers a diverse range of programs including B.Tech, BBA, B.Com, B.Des, integrated law, M.Tech, MBA, LLM, and Ph.D. courses, enrolling over 5,000 students annually. The university emphasizes multidisciplinary education, blending engineering with management, design, and law. Placements are solid, with 80% of students placed in 2023 at companies like Infosys, Capgemini, and EY, with a peak package of ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"64 acres\"\n        },\n        {\n            \"id\": 27,\n            \"name\": \"Nagarjuna College of Engineering and Technology\",\n            \"acronym\": \"NCET\",\n            \"ranking\": 27,\n            \"address\": \"NCET, 38/1, Mudugurki, Devanahalli, Bengaluru, Karnataka - 562164\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2335\\xb0 N, 77.7119\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Artificial engineering and machine learning,  Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Construction Technology, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, Mindtree, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements with IT focus.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, surrounded by rural greenery.\",\n            \"labs\": \"Labs for Civil Engineering, CSE, ECE, and Mechanical, equipped for practical training with basic research capabilities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Devanahalli; no direct metro, ~30 km from Bangalore city center, reliant on college transport or private vehicles.\",\n            \"summary\": \"Nagarjuna College of Engineering and Technology (NCET), established in 2001 by the Nagarjuna Education Society, is a VTU-affiliated autonomous college located in Mudugurki, Devanahalli, approximately 30 km from Bangalore near the Kempegowda International Airport. Spanning an expansive 100-acre campus, NCET offers five B.E. programs, three M.Tech specializations, MBA, and Ph.D. courses, catering to around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NCET\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 28,\n            \"name\": \"Sri Venkateswara College of Engineering\",\n            \"acronym\": \"SVCE\",\n            \"ranking\": 28,\n            \"address\": \"SVCE, NH-7, Vidyanagar, Kempegowda International Airport Road, Bengaluru, Karnataka - 562157\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1547\\xb0 N, 77.6138\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Artificial engineering and machine learning , Computer science and engineering (data science), Computer science and engineering (cyber security) ,Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: MBA: General\",\n            \"placementDetails\": \"70% placement rate; companies include Infosys, HCL, Tech Mahindra, TCS; highest package ~₹20 LPA (2023). Modest placements with IT focus.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"20-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a semi-rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for basic practical training with limited research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on NH-7; no direct metro, near Airport Road, ~25 km from Bangalore city center, reliant on college transport.\",\n            \"summary\": \"Sri Venkateswara College of Engineering (SVCE), established in 2001 by the Sri Venkateswara Educational and Charitable Trust, is a VTU-affiliated college located in Vidyanagar, along NH-7 near Kempegowda International Airport, about 25 km from Bangalore's core. Its 20-acre campus offers six B.E. programs, two M.Tech specializations, and an MBA, serving approximately 1,500 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SVCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"20 acres\"\n        },\n        {\n            \"id\": 29,\n            \"name\": \"SJ College of Engineering (SJCE Mysore)\",\n            \"acronym\": \"SJCE\",\n            \"ranking\": 29,\n            \"address\": \"JSS Science and Technology University (SJCE), JSS TI Campus, Manasagangothri, Mysuru, Karnataka - 570006 (Not Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3135\\xb0 N, 76.6738\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Computer Science and Business Systems, Construction Technology and Management, Electrical and Electronics Engineering, Artificial engineering and machine learning ,Electronics and Communication Engineering, Environmental Engineering, Industrial and Production Engineering, Information Science and Engineering, Mechanical Engineering, Polymer Science and Technology - **Postgraduate (M.Tech)**: Automotive Electronics, Biotechnology, Computer Engineering, Energy Systems and Management, Environmental Engineering, Industrial Electronics, Maintenance Engineering, Software Engineering, Structural Engineering - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering, Management, Sciences\",\n            \"placementDetails\": \"90% placement rate; companies include Infosys, Cisco, L&T, TCS; highest package ~₹40 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"117-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, auditorium, and dining facilities.\",\n            \"labs\": \"Labs for Biotech, Robotics, Structural Engineering, and Polymer Science, equipped for advanced research and training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses serve Mysuru; no metro in Mysuru, ~120 km from Bangalore, requiring intercity travel.\",\n            \"summary\": \"JSS Science and Technology University (SJCE), originally established as Sri Jayachamarajendra College of Engineering in 1963 by the JSS Mahavidyapeetha, is a premier autonomous institution in Mysuru, Karnataka, approximately 120 km from Bangalore. Transitioning to university status in 2016, its 117-acre campus in Manasagangothri offers 12 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses, enrolling over 4,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"117 acres\"\n        },\n        {\n            \"id\": 30,\n            \"name\": \"SJC Institute of Technology\",\n            \"acronym\": \"SJCIT\",\n            \"ranking\": 30,\n            \"address\": \"SJCIT, P.B. No. 20, B.B. Road, Chickballapur, Karnataka - 562101 (Near Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.4351\\xb0 N, 77.7315\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning , Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Communication and Networking, Machine Design, Structural Engineering - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"53-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training with basic research scope.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Chickballapur; no direct metro, ~50 km from Bangalore, reliant on college transport or private vehicles.\",\n            \"summary\": \"SJC Institute of Technology (SJCIT), established in 1986 by the Sri Adichunchanagiri Shikshana Trust, is a VTU-affiliated college located in Chickballapur, about 50 km north of Bangalore. Its expansive 53-acre campus offers six B.E. programs, four M.Tech specializations, MBA, and Ph.D. courses, serving around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"53 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map(([range, count])=>({\n            range,\n            count\n        }));\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort(([, a], [, b])=>b - a).slice(0, 10).map(([company, mentions])=>({\n            company,\n            mentions\n        }));\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return `₹${(amount / 100).toFixed(2)} Cr`;\n    }\n    return `₹${amount} LPA`;\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return `${size} acres`;\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : `NIRF ${nirf}`;\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = (collegeName = \"\")=>{\n    const message = collegeName ? `Hi! I'm interested in learning more about ${collegeName} and would like a free consultation.` : `Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?`;\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/collegeData.js\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f884c0db3e83\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmFuZ2Fsb3JlLWVuZ2luZWVyaW5nLWNvbGxlZ2VzLy4vc3JjL3N0eWxlcy9nbG9iYWxzLmNzcz83NzlhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjg4NGMwZGIzZTgzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Header */ \"(rsc)/./src/components/Header.js\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Footer */ \"(rsc)/./src/components/Footer.js\");\n\n\n\n\nconst metadata = {\n    title: \"Top Engineering Colleges in Bangalore | College Comparison & Rankings\",\n    description: \"Compare top engineering colleges in Bangalore. Get detailed information about placements, courses, rankings, and campus facilities. Find your perfect engineering college match.\",\n    keywords: \"engineering colleges bangalore, college comparison, placement statistics, RVCE, MSRIT, PES University, BMS College, college rankings\",\n    authors: [\n        {\n            name: \"College Comparison Platform\"\n        }\n    ],\n    openGraph: {\n        title: \"Top Engineering Colleges in Bangalore | College Comparison\",\n        description: \"Compare top engineering colleges in Bangalore with detailed placement statistics, course information, and campus facilities.\",\n        type: \"website\",\n        locale: \"en_IN\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Top Engineering Colleges in Bangalore\",\n        description: \"Compare engineering colleges in Bangalore with detailed insights.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebSite\",\n                                \"name\": \"Bangalore Engineering Colleges Comparison\",\n                                \"description\": \"Compare top engineering colleges in Bangalore with detailed placement statistics and course information\",\n                                \"url\": \"https://bangalore-engineering-colleges.com\",\n                                \"potentialAction\": {\n                                    \"@type\": \"SearchAction\",\n                                    \"target\": \"https://bangalore-engineering-colleges.com/colleges?search={search_term_string}\",\n                                    \"query-input\": \"required name=search_term_string\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gray-50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-6 right-6 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-bounce-slow\",\n                            \"aria-label\": \"WhatsApp Consultation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\education 1\src\app\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Footer.js":
/*!**********************************!*\
  !*** ./src/components/Footer.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction Footer() {\n    const quickLinks = [\n        {\n            name: \"All Colleges\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Top Ranked\",\n            href: \"/colleges?sort=ranking\"\n        },\n        {\n            name: \"Best Placements\",\n            href: \"/colleges?sort=placementRate\"\n        },\n        {\n            name: \"Compare Colleges\",\n            href: \"/compare\"\n        }\n    ];\n    const topColleges = [\n        {\n            name: \"RVCE\",\n            href: \"/colleges/1\"\n        },\n        {\n            name: \"MSRIT\",\n            href: \"/colleges/6\"\n        },\n        {\n            name: \"PES University\",\n            href: \"/colleges/4\"\n        },\n        {\n            name: \"BMS College\",\n            href: \"/colleges/5\"\n        }\n    ];\n    const resources = [\n        {\n            name: \"Placement Statistics\",\n            href: \"/colleges?sort=placementRate\"\n        },\n        {\n            name: \"Course Information\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Campus Facilities\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Admission Guide\",\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-max section-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-primary-600 to-secondary-600 p-2 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Bangalore Engineering\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"College Comparison\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6 leading-relaxed\",\n                                    children: \"Your trusted guide to finding the perfect engineering college in Bangalore. Compare placements, courses, and facilities to make an informed decision.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Bangalore, Karnataka, India\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Top Colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: topColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: college.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: college.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, college.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 mb-6\",\n                                    children: resources.map((resource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: resource.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: resource.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, resource.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+91 98765 43210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"\\xa9 2024 Bangalore Engineering College Comparison. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFDOEM7QUFFNUQsU0FBU007SUFDdEIsTUFBTUMsYUFBYTtRQUNqQjtZQUFFQyxNQUFNO1lBQWdCQyxNQUFNO1FBQVk7UUFDMUM7WUFBRUQsTUFBTTtZQUFjQyxNQUFNO1FBQXlCO1FBQ3JEO1lBQUVELE1BQU07WUFBbUJDLE1BQU07UUFBK0I7UUFDaEU7WUFBRUQsTUFBTTtZQUFvQkMsTUFBTTtRQUFXO0tBQzlDO0lBRUQsTUFBTUMsY0FBYztRQUNsQjtZQUFFRixNQUFNO1lBQVFDLE1BQU07UUFBYztRQUNwQztZQUFFRCxNQUFNO1lBQVNDLE1BQU07UUFBYztRQUNyQztZQUFFRCxNQUFNO1lBQWtCQyxNQUFNO1FBQWM7UUFDOUM7WUFBRUQsTUFBTTtZQUFlQyxNQUFNO1FBQWM7S0FDNUM7SUFFRCxNQUFNRSxZQUFZO1FBQ2hCO1lBQUVILE1BQU07WUFBd0JDLE1BQU07UUFBK0I7UUFDckU7WUFBRUQsTUFBTTtZQUFzQkMsTUFBTTtRQUFZO1FBQ2hEO1lBQUVELE1BQU07WUFBcUJDLE1BQU07UUFBWTtRQUMvQztZQUFFRCxNQUFNO1lBQW1CQyxNQUFNO1FBQUk7S0FDdEM7SUFFRCxxQkFDRSw4REFBQ0c7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7OzhCQUNiLDhEQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2Isa0RBQUlBO29DQUFDUyxNQUFLO29DQUFJSSxXQUFVOztzREFDdkIsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUNiLDRFQUFDWixtSEFBUUE7Z0RBQUNZLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ0M7OzhEQUNDLDhEQUFDQztvREFBR0YsV0FBVTs4REFBb0I7Ozs7Ozs4REFDbEMsOERBQUNHO29EQUFFSCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUd6Qyw4REFBQ0c7b0NBQUVILFdBQVU7OENBQXFDOzs7Ozs7OENBSWxELDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNULG1IQUFNQTs0Q0FBQ1MsV0FBVTs7Ozs7O3NEQUNsQiw4REFBQ0k7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLViw4REFBQ0g7OzhDQUNDLDhEQUFDSTtvQ0FBR0wsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FDM0MsOERBQUNNO29DQUFHTixXQUFVOzhDQUNYTixXQUFXYSxHQUFHLENBQUMsQ0FBQ0MscUJBQ2YsOERBQUNDO3NEQUNDLDRFQUFDdEIsa0RBQUlBO2dEQUNIUyxNQUFNWSxLQUFLWixJQUFJO2dEQUNmSSxXQUFVOztrRUFFViw4REFBQ0k7a0VBQU1JLEtBQUtiLElBQUk7Ozs7OztrRUFDaEIsOERBQUNILG1IQUFZQTt3REFBQ1EsV0FBVTs7Ozs7Ozs7Ozs7OzJDQU5uQlEsS0FBS2IsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FjeEIsOERBQUNNOzs4Q0FDQyw4REFBQ0k7b0NBQUdMLFdBQVU7OENBQTZCOzs7Ozs7OENBQzNDLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FDWEgsWUFBWVUsR0FBRyxDQUFDLENBQUNHLHdCQUNoQiw4REFBQ0Q7c0RBQ0MsNEVBQUN0QixrREFBSUE7Z0RBQ0hTLE1BQU1jLFFBQVFkLElBQUk7Z0RBQ2xCSSxXQUFVOztrRUFFViw4REFBQ0k7a0VBQU1NLFFBQVFmLElBQUk7Ozs7OztrRUFDbkIsOERBQUNILG1IQUFZQTt3REFBQ1EsV0FBVTs7Ozs7Ozs7Ozs7OzJDQU5uQlUsUUFBUWYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FjM0IsOERBQUNNOzs4Q0FDQyw4REFBQ0k7b0NBQUdMLFdBQVU7OENBQTZCOzs7Ozs7OENBQzNDLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FDWEYsVUFBVVMsR0FBRyxDQUFDLENBQUNJLHlCQUNkLDhEQUFDRjtzREFDQyw0RUFBQ3RCLGtEQUFJQTtnREFDSFMsTUFBTWUsU0FBU2YsSUFBSTtnREFDbkJJLFdBQVU7O2tFQUVWLDhEQUFDSTtrRUFBTU8sU0FBU2hCLElBQUk7Ozs7OztrRUFDcEIsOERBQUNILG1IQUFZQTt3REFBQ1EsV0FBVTs7Ozs7Ozs7Ozs7OzJDQU5uQlcsU0FBU2hCLElBQUk7Ozs7Ozs7Ozs7OENBYTFCLDhEQUFDTTtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ1YsbUhBQUtBO29EQUFDVSxXQUFVOzs7Ozs7OERBQ2pCLDhEQUFDSTs4REFBSzs7Ozs7Ozs7Ozs7O3NEQUVSLDhEQUFDSDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNYLG1IQUFJQTtvREFBQ1csV0FBVTs7Ozs7OzhEQUNoQiw4REFBQ0k7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPZCw4REFBQ0g7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQXdCOzs7Ozs7MENBR3ZDLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNiLGtEQUFJQTt3Q0FBQ1MsTUFBSzt3Q0FBSUksV0FBVTtrREFBa0Q7Ozs7OztrREFHM0UsOERBQUNiLGtEQUFJQTt3Q0FBQ1MsTUFBSzt3Q0FBSUksV0FBVTtrREFBa0Q7Ozs7OztrREFHM0UsOERBQUNiLGtEQUFJQTt3Q0FBQ1MsTUFBSzt3Q0FBSUksV0FBVTtrREFBa0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTekYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iYW5nYWxvcmUtZW5naW5lZXJpbmctY29sbGVnZXMvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIuanM/OWRmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgQm9va09wZW4sIE1haWwsIFBob25lLCBNYXBQaW4sIEV4dGVybmFsTGluayB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZvb3RlcigpIHtcbiAgY29uc3QgcXVpY2tMaW5rcyA9IFtcbiAgICB7IG5hbWU6ICdBbGwgQ29sbGVnZXMnLCBocmVmOiAnL2NvbGxlZ2VzJyB9LFxuICAgIHsgbmFtZTogJ1RvcCBSYW5rZWQnLCBocmVmOiAnL2NvbGxlZ2VzP3NvcnQ9cmFua2luZycgfSxcbiAgICB7IG5hbWU6ICdCZXN0IFBsYWNlbWVudHMnLCBocmVmOiAnL2NvbGxlZ2VzP3NvcnQ9cGxhY2VtZW50UmF0ZScgfSxcbiAgICB7IG5hbWU6ICdDb21wYXJlIENvbGxlZ2VzJywgaHJlZjogJy9jb21wYXJlJyB9LFxuICBdO1xuXG4gIGNvbnN0IHRvcENvbGxlZ2VzID0gW1xuICAgIHsgbmFtZTogJ1JWQ0UnLCBocmVmOiAnL2NvbGxlZ2VzLzEnIH0sXG4gICAgeyBuYW1lOiAnTVNSSVQnLCBocmVmOiAnL2NvbGxlZ2VzLzYnIH0sXG4gICAgeyBuYW1lOiAnUEVTIFVuaXZlcnNpdHknLCBocmVmOiAnL2NvbGxlZ2VzLzQnIH0sXG4gICAgeyBuYW1lOiAnQk1TIENvbGxlZ2UnLCBocmVmOiAnL2NvbGxlZ2VzLzUnIH0sXG4gIF07XG5cbiAgY29uc3QgcmVzb3VyY2VzID0gW1xuICAgIHsgbmFtZTogJ1BsYWNlbWVudCBTdGF0aXN0aWNzJywgaHJlZjogJy9jb2xsZWdlcz9zb3J0PXBsYWNlbWVudFJhdGUnIH0sXG4gICAgeyBuYW1lOiAnQ291cnNlIEluZm9ybWF0aW9uJywgaHJlZjogJy9jb2xsZWdlcycgfSxcbiAgICB7IG5hbWU6ICdDYW1wdXMgRmFjaWxpdGllcycsIGhyZWY6ICcvY29sbGVnZXMnIH0sXG4gICAgeyBuYW1lOiAnQWRtaXNzaW9uIEd1aWRlJywgaHJlZjogJyMnIH0sXG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtd2hpdGVcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyLW1heCBzZWN0aW9uLXBhZGRpbmdcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC04XCI+XG4gICAgICAgICAgey8qIEJyYW5kIFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LTYwMCB0by1zZWNvbmRhcnktNjAwIHAtMiByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPkJhbmdhbG9yZSBFbmdpbmVlcmluZzwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+Q29sbGVnZSBDb21wYXJpc29uPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgWW91ciB0cnVzdGVkIGd1aWRlIHRvIGZpbmRpbmcgdGhlIHBlcmZlY3QgZW5naW5lZXJpbmcgY29sbGVnZSBpbiBCYW5nYWxvcmUuIFxuICAgICAgICAgICAgICBDb21wYXJlIHBsYWNlbWVudHMsIGNvdXJzZXMsIGFuZCBmYWNpbGl0aWVzIHRvIG1ha2UgYW4gaW5mb3JtZWQgZGVjaXNpb24uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+QmFuZ2Fsb3JlLCBLYXJuYXRha2EsIEluZGlhPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUXVpY2sgTGlua3MgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNlwiPlF1aWNrIExpbmtzPC9oND5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge3F1aWNrTGlua3MubWFwKChsaW5rKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxpIGtleT17bGluay5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2xpbmsuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBncm91cFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntsaW5rLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8RXh0ZXJuYWxMaW5rIGNsYXNzTmFtZT1cImgtMyB3LTMgbWwtMSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTIwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRvcCBDb2xsZWdlcyAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi02XCI+VG9wIENvbGxlZ2VzPC9oND5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge3RvcENvbGxlZ2VzLm1hcCgoY29sbGVnZSkgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e2NvbGxlZ2UubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICBocmVmPXtjb2xsZWdlLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Y29sbGVnZS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJoLTMgdy0zIG1sLTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0yMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBSZXNvdXJjZXMgJiBDb250YWN0ICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTZcIj5SZXNvdXJjZXM8L2g0PlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi02XCI+XG4gICAgICAgICAgICAgIHtyZXNvdXJjZXMubWFwKChyZXNvdXJjZSkgPT4gKFxuICAgICAgICAgICAgICAgIDxsaSBrZXk9e3Jlc291cmNlLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17cmVzb3VyY2UuaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBncm91cFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntyZXNvdXJjZS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPEV4dGVybmFsTGluayBjbGFzc05hbWU9XCJoLTMgdy0zIG1sLTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0yMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC91bD5cblxuICAgICAgICAgICAgey8qIENvbnRhY3QgSW5mbyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIDxQaG9uZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj4rOTEgOTg3NjUgNDMyMTA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5pbmZvQGJhbmdhbG9yZWNvbGxlZ2VzLmNvbTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEJvdHRvbSBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTgwMCBtdC0xMiBwdC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgc3BhY2UteS00IG1kOnNwYWNlLXktMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgwqkgMjAyNCBCYW5nYWxvcmUgRW5naW5lZXJpbmcgQ29sbGVnZSBDb21wYXJpc29uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNiB0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICAgIFByaXZhY3kgUG9saWN5XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICAgIFRlcm1zIG9mIFNlcnZpY2VcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAgQ29udGFjdCBVc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiQm9va09wZW4iLCJNYWlsIiwiUGhvbmUiLCJNYXBQaW4iLCJFeHRlcm5hbExpbmsiLCJGb290ZXIiLCJxdWlja0xpbmtzIiwibmFtZSIsImhyZWYiLCJ0b3BDb2xsZWdlcyIsInJlc291cmNlcyIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImgzIiwicCIsInNwYW4iLCJoNCIsInVsIiwibWFwIiwibGluayIsImxpIiwiY29sbGVnZSIsInJlc291cmNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.js\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\education 1\src\components\Header.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();