"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: function() { return /* binding */ colleges; },\n/* harmony export */   filterColleges: function() { return /* binding */ filterColleges; },\n/* harmony export */   formatCampusSize: function() { return /* binding */ formatCampusSize; },\n/* harmony export */   formatCurrency: function() { return /* binding */ formatCurrency; },\n/* harmony export */   formatNIRF: function() { return /* binding */ formatNIRF; },\n/* harmony export */   getAggregateStats: function() { return /* binding */ getAggregateStats; },\n/* harmony export */   getAllColleges: function() { return /* binding */ getAllColleges; },\n/* harmony export */   getCollegeById: function() { return /* binding */ getCollegeById; },\n/* harmony export */   getFeaturedColleges: function() { return /* binding */ getFeaturedColleges; },\n/* harmony export */   getPlacementStats: function() { return /* binding */ getPlacementStats; },\n/* harmony export */   getTopCompanies: function() { return /* binding */ getTopCompanies; },\n/* harmony export */   getWhatsAppLink: function() { return /* binding */ getWhatsAppLink; },\n/* harmony export */   searchColleges: function() { return /* binding */ searchColleges; },\n/* harmony export */   sortColleges: function() { return /* binding */ sortColleges; }\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if ( true && !collegesData) {\n        try {\n            const response = await fetch(\"/colleges.json\");\n            collegesData = await response.json();\n        } catch (error) {\n            console.error(\"Failed to load college data:\", error);\n            collegesData = getFallbackData();\n        }\n    }\n    return collegesData || getFallbackData();\n};\n// Complete dataset from college.json - All 50 colleges\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"MS Ramaiah Institute of Technology\",\n            \"acronym\": \"MSRIT\",\n            \"ranking\": 6,\n            \"address\": \"MS Ramaiah Institute of Technology, MSR Nagar, MSRIT Post, Bengaluru, Karnataka - 560054.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0306\\xb0 N, 77.5653\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, , Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Integrated Manufacturing, Computer Science and Engineering, Digital Communication, Digital Electronics and Communication, Industrial Engineering, Manufacturing Science and Engineering, Software Engineering, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: B.Arch: Architecture; MBA: General; MCA: General; Ph.D.: All engineering disciplines, Architecture, Management.\",\n            \"placementDetails\": \"95% placement rate; companies include Amazon, Capgemini, Intel, TCS; highest package ~₹50 LPA (2023). Excellent tech and core placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 50,\n            \"infrastructure\": \"25-acre campus with smart classrooms, central library, hostels, sports complex (cricket, volleyball), gym, auditorium, and dining halls.\",\n            \"labs\": \"Advanced labs for AI, VLSI, Structural Engineering, Biotech, and Mechanical Engineering, equipped for research and industry projects.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve MSR Nagar; Sandal Soap Factory Metro Station (Green Line), ~2 km, accessible by auto or walk.\",\n            \"summary\": \"MSRIT, founded in 1962 by Dr. M.S. Ramaiah, spans 25 acres in North Bangalore. It offers 12 B.E. programs, 12 M.Tech specializations, and strong placements at firms like Amazon and Intel, peaking at ₹50 LPA in 2023. Labs support cutting-edge research, and connectivity includes BMTC buses and the Green Line metro.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1962,\n            \"nirf\": \"78\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"Sir M Visvesvaraya Institute of Technology\",\n            \"acronym\": \"Sir MVIT\",\n            \"ranking\": 7,\n            \"address\": \"Sir MVIT, Krishnadevaraya Nagar, Hunasamaranahalli, International Airport Road, Bengaluru, Karnataka - 562157.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1507\\xb0 N, 77.6082\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Computer science and engineering (cyber security and IoT) Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Integrated Manufacturing, Electronics, Mechanical Engineering (Design) - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines, Management.\",\n            \"placementDetails\": \"80%+ placement rate; companies include TCS, Wipro, Cognizant, Infosys; highest package ~₹30 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"133-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, auditorium, and canteens.\",\n            \"labs\": \"Labs for Electronics, Mechanical, Biotech, and Computer Science, supporting practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Airport Road; no direct metro, ~20 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Sir MVIT, established in 1986, spans a vast 133-acre campus on International Airport Road. It offers eight B.E. programs and reliable placements at firms like TCS and Wipro, peaking at ₹30 LPA in 2023. While its rural location limits accessibility, the large campus provides ample space for expansion.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SirMVIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"133 acres\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"Bangalore Institute of Technology\",\n            \"acronym\": \"BIT\",\n            \"ranking\": 8,\n            \"address\": \"BIT, K.R. Road, V.V. Puram, Bengaluru, Karnataka - 560004.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9561\\xb0 N, 77.5762\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Dell, Accenture, TCS; highest package ~₹37 LPA (2023). Strong IT and core engineering focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 37,\n            \"infrastructure\": \"5-acre urban campus with classrooms, library, hostels, sports area (volleyball, badminton), auditorium, and canteens.\",\n            \"labs\": \"Labs for AI, VLSI, Civil, Mechanical, and Electronics, equipped for practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent K.R. Road; Chickpet Metro Station (Green Line), ~1.5 km, highly accessible.\",\n            \"summary\": \"BIT, founded in 1979 under the Vokkaligara Sangha, is a well-regarded VTU-affiliated college in central Bangalore. Its compact 5-acre campus hosts nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Placements are strong, with 85% of students placed in 2023 at firms like Infosys and Dell, peaking at ₹37 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Nitte Meenakshi Institute of Technology\",\n            \"acronym\": \"NMIT\",\n            \"ranking\": 9,\n            \"address\": \"NMIT, P.B. No. 6429, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Artificial engineering and machine learning, Artificial learning and data science, Computer Science and Engineering, Computer Science and Engineering (Business system), Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Data Sciences, Machine Design, Renewable Energy, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Microsoft, Infosys, Huawei, TCS; highest package ~₹40 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"23-acre campus with modern classrooms, library, hostels, sports facilities (cricket, tennis), gym, and dining halls.\",\n            \"labs\": \"Robotics Lab, Aerospace Lab, Data Science Lab, VLSI Lab, and Mechanical Workshop, supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve Yelahanka; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"NMIT, established in 2001 by the Nitte Education Trust, is an autonomous VTU-affiliated college in Yelahanka. Spanning 23 acres, it offers seven B.E. programs, six M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it emphasizes innovation and research, with over 50 patents filed.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NMIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"23 acres\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"PES University (Electronic City Campus)\",\n            \"acronym\": \"PESUECC\",\n            \"ranking\": 10,\n            \"address\": \"PES University, Electronic City Campus, Hosur Road, Electronic City, Bengaluru, Karnataka - 560100.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Artificial engineering and machine learning,  Electronics and Communication Engineering, Mechanical Engineering - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Automotive Engineering); MBA: Finance, Marketing, HR; MCA: General - **Other Programs**: Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Amazon, Intel, Flipkart, TCS; highest package ~₹60 LPA (2023). Excellent tech placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 60,\n            \"infrastructure\": \"50-acre campus with advanced classrooms, library, hostels, sports complex (football, basketball), gym, and cafeterias.\",\n            \"labs\": \"Labs for AI, Electronics, Automotive Engineering, and Software Development, equipped with industry-grade tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Electronic City Metro Station (upcoming Yellow Line, ~2 km), currently reliant on buses or autos.\",\n            \"summary\": \"PES University's Electronic City Campus, established in 2005, offers three B.Tech programs, three M.Tech specializations, MBA, MCA, and Ph.D. courses. Proximity to Electronic City enhances tech exposure. Ranked alongside its Ring Road counterpart in NIRF 2024 (101-150, University), this campus benefits from PES's legacy and alumni like Nishanth Ananthram (Google).\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESUECC\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2005,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"CMR Institute of Technology\",\n            \"acronym\": \"CMRIT\",\n            \"ranking\": 11,\n            \"address\": \"CMRIT, 132, AECS Layout, ITPL Main Road, Kundalahalli, Bengaluru, Karnataka - 560037.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Capgemini, IBM, Infosys; highest package ~₹30 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"8-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and VLSI, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on ITPL Road; Kundalahalli Metro Station (Purple Line), ~2 km, easily accessible.\",\n            \"summary\": \"CMRIT, founded in 2000 by the CMR Jnanadhara Trust, is a VTU-affiliated autonomous college in East Bangalore. Its compact 8-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it focuses on industry readiness.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=CMRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"8 acres\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"Dayananda Sagar College of Engineering\",\n            \"acronym\": \"DSCE\",\n            \"ranking\": 12,\n            \"address\": \"DSCE, Shavige Malleshwara Hills, Kumaraswamy Layout, Bengaluru, Karnataka - 560078.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Artificial engineering and machine learning, Computer science and engineering (data science), Computer science and engineering (cyber security), Computer science and engineering (Internet of things and block chain technology), Computer science and Business system, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Instrumentation Technology, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bioinformatics, Computer Integrated Manufacturing, Computer Science and Engineering, Design Engineering, Digital Electronics and Communication, Highway Technology, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Accenture, Cognizant, L&T, Infosys; highest package ~₹40 LPA (2023). Balanced IT and core placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"29-acre campus with modern classrooms, library, hostels, sports arena (cricket, basketball), gym, and dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Civil, VLSI, and Mechanical, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"DSCE, established in 1979, is a leading VTU-affiliated autonomous institute in South Bangalore. Its expansive 29-acre campus offers 14 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it boasts modern infrastructure and robust placements at firms like Accenture and L&T, peaking at ₹40 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"29 acres\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"BMS Institute of Technology\",\n            \"acronym\": \"BMSIT\",\n            \"ranking\": 13,\n            \"address\": \"BMSIT, Doddaballapur Main Road, Avalahalli, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1351\\xb0 N, 77.5718\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Computer science and business system, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Wipro, Dell, TCS; highest package ~₹35 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"21-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, and canteens.\",\n            \"labs\": \"Labs for AI, IoT, Electronics, and Mechanical, equipped for practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Doddaballapur Road; Yelahanka Metro Station (Green Line), ~6 km, requiring auto or bus.\",\n            \"summary\": \"BMSIT, established in 2002 as a sister institution to BMSCE, offers eight B.E. programs, two M.Tech specializations, MCA, and Ph.D. courses. Autonomous since 2016, it aligns with industry trends and offers a balanced environment with green spaces.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"21 acres\"\n        },\n        {\n            \"id\": 14,\n            \"name\": \"Reva University\",\n            \"acronym\": \"REVA\",\n            \"ranking\": 14,\n            \"address\": \"Reva University, Rukmini Knowledge Park, Kattigenahalli, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1167\\xb0 N, 77.6344\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Civil Engineering, Computer Science and Engineering, Computer Science and IT, Computer Science and system, Artificial learning and data science, Computer science and engineering (IoT & cyber security including blockchain technology), CSE (AI & ML), CSE (Cybersecurity), Electronics and Communication Engineering, Electronics and Computer  Engineering,  Mechanical Engineering, Bioelectronics, Robotics and Automation - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design, Power Electronics, Structural Engineering - MBA: Finance, Marketing, HR - MCA: General - M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts\",\n            \"placementDetails\": \"80% placement rate; companies include Amazon, TCS, IBM, Infosys; highest package ~₹45 LPA (2023). Strong tech placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"45-acre campus with modern classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Robotics, Cloud Computing, Civil Engineering, and VLSI equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kattigenahalli Road; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Reva University, established in 2004 as Reva Institute of Technology and granted university status in 2012, is a private institution in Kattigenahalli, Yelahanka, North Bangalore. Its 45-acre campus offers eight B.Tech specializations, BBA, B.Arch, M.Tech, MBA, M.Des, and Ph.D. courses, enrolling over 15,000 students across disciplines. Reva emphasizes multidisciplinary education blending engineering with management and design, featuring programs like Cybersecurity and Robotics. Labs support innovation, and placements are excellent, with 80% of students placed at firms like Amazon and TCS, peaking at ₹45 LPA. Ranked 151-200 in NIRF 2024 (University), it focuses on employability and entrepreneurship.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=REVA\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2004,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"45 acres\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"MS Ramaiah University of Applied Sciences\",\n            \"acronym\": \"MSRUAS\",\n            \"ranking\": 15,\n            \"address\": \"MSRUAS, University House, Gnanagangothri Campus, New BEL Road, MSR Nagar, Bengaluru, Karnataka - 560054\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0309\\xb0 N, 77.5643\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Aerospace Engineering, Artificial engineering and machine learning , Information science and engineering, Mathematics and computing, Robotics, Automotive Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Mechanical Engineering - B.Des: Product Design, Fashion Design - BBA: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Aircraft Design, Automotive Electronics, Data Sciences, Structural Engineering - MBA: General - M.Des: Product Design - M.Pharm: Pharmaceutics - **Other Programs**: Ph.D.: Engineering, Design, Pharmacy, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, HCL, Bosch, TCS; highest package ~₹40 LPA (2023). Balanced tech and core placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"25-acre campus with advanced classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aerospace, Automotive, Data Sciences, and Pharmacy equipped for research and industry collaboration.\",\n            \"busAndMetroConvenience\": \"BMTC buses on New BEL Road; Sandal Soap Factory Metro Station (Green Line), ~2 km, easily accessible.\",\n            \"summary\": \"MS Ramaiah University of Applied Sciences (MSRUAS), established in 2013 under the Gokula Education Foundation, is located in MSR Nagar, North Bangalore. Its 25-acre campus offers seven B.Tech programs, B.Des, BBA, B.Pharm, M.Tech, MBA, M.Des, M.Pharm, and Ph.D. courses, serving over 5,000 students. MSRUAS emphasizes applied learning with niche programs like Aircraft Design and Automotive Engineering. Labs drive research, and placements are solid, with 85% of students placed at firms like Infosys and Bosch, peaking at ₹40 LPA. Connectivity is excellent, with BMTC buses and the Green Line metro station nearby.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRUAS\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 16,\n            \"name\": \"Siddaganga Institute of Technology\",\n            \"acronym\": \"SIT\",\n            \"ranking\": 16,\n            \"address\": \"SIT, B.H. Road, Tumakuru, Karnataka - 572103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.3409\\xb0 N, 77.1180\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹25 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Biotech, Chemical, Civil, and Electronics, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Tumakuru; no metro connectivity, ~70 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"SIT, established in 1963 by the Siddaganga Education Society, is located in Tumakuru, 70 km from Bangalore. Its sprawling 100-acre campus offers nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes discipline and values-based education. Placements are reliable, with 80% of students placed at firms like TCS and Infosys, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 17,\n            \"name\": \"JSS Science and Technology University\",\n            \"acronym\": \"JSSTU\",\n            \"ranking\": 17,\n            \"address\": \"JSSTU, JSS Technical Institutions Campus, Mysuru, Karnataka - 570006\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3375\\xb0 N, 76.6244\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.E.: Aeronautical Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics - B.Arch: Architecture - **Postgraduate**: M.Tech: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - MBA: General - M.Arch: Architecture - **Other Programs**: Ph.D.: Engineering, Architecture, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, TCS, Wipro, L&T; highest package ~₹30 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"200-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Chemical, and Electronics, equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses within Mysuru; no metro connectivity, ~150 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"JSS Science and Technology University (JSSTU), established in 1963 and granted university status in 2008, is located in Mysuru. Its expansive 200-acre campus offers 11 B.E. programs, B.Arch, M.Tech, MBA, M.Arch, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes research and innovation. Placements are strong, with 85% of students placed at firms like Infosys and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=JSSTU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"200 acres\"\n        },\n        {\n            \"id\": 18,\n            \"name\": \"Sapthagiri College of Engineering\",\n            \"acronym\": \"SCE\",\n            \"ranking\": 18,\n            \"address\": \"SCE, 14/5, Chikkasandra, Hesaraghatta Main Road, Bengaluru, Karnataka - 560057\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0833\\xb0 N, 77.5167\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹20 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"15-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; no direct metro, ~15 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"SCE, established in 1999 by the Sapthagiri Educational Trust, is located in Chikkasandra, North Bangalore. Its 15-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it focuses on affordable quality education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1999,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"15 acres\"\n        },\n        {\n            \"id\": 19,\n            \"name\": \"Atria Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 19,\n            \"address\": \"AIT, Anandnagar, Hebbal, Bengaluru, Karnataka - 560024\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0358\\xb0 N, 77.5970\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, Accenture; highest package ~₹25 LPA (2023). Solid IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, badminton), gym, canteens.\",\n            \"labs\": \"Labs for CSE, VLSI, Electronics, and Mechanical, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hebbal Road; Hebbal Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"AIT, established in 2000 by the Atria Educational Trust, is located in Anandnagar, Hebbal, North Bangalore. Its compact 10-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2016, it emphasizes industry readiness. Placements are good, with 80% of students placed at firms like Infosys and TCS, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 20,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT_Acharya\",\n            \"ranking\": 20,\n            \"address\": \"Acharya Institute of Technology, Acharya Dr. Sarvepalli Radhakrishnan Road, Soldevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1000\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹22 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, CSE, ECE, and Mechanical, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soldevanahalli Road; no direct metro, ~10 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Acharya Institute of Technology, established in 2000 by the Acharya Institutes, is located in Soldevanahalli, North Bangalore. Its sprawling 120-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it emphasizes holistic education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT_Acharya\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 21,\n            \"name\": \"New Horizon College of Engineering\",\n            \"acronym\": \"NHCE\",\n            \"ranking\": 21,\n            \"address\": \"NHCE, Near Marathahalli, Outer Ring Road, Bengaluru, Karnataka - 560103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9591\\xb0 N, 77.7085\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, IBM; highest package ~₹28 LPA (2023). Good IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 28,\n            \"infrastructure\": \"62-acre campus with modern classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aeronautics, VLSI, CSE, and Electronics, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Outer Ring Road; Marathahalli Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"NHCE, established in 1982 by the New Horizon Educational Trust, is located near Marathahalli on the Outer Ring Road. Its 62-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2007, it emphasizes innovation and entrepreneurship. Placements are good, with 80% of students placed at firms like Infosys and IBM, peaking at ₹28 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NHCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1982,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"62 acres\"\n        },\n        {\n            \"id\": 22,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 22,\n            \"address\": \"Presidency University, Itgalpura, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2167\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering, Electrical and Electronics Engineering; BBA: General; B.Com: General; B.A.: Various specializations; B.Sc.: Various specializations; B.Des: Product Design; B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design; MBA: General; M.Com: General; M.A.: Various specializations; M.Sc.: Various specializations; M.Des: Product Design; M.Pharm: Pharmacy - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts, Pharmacy\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹18 LPA (2023). Entry-level placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Pharmacy, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; no direct metro, ~15 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Presidency University, established in 2013, is a private university located in Itgalpura, Rajanakunte, North Bangalore. Its expansive 100-acre campus offers five B.Tech programs, BBA, B.Com, B.A., B.Sc., B.Des, B.Pharm, M.Tech, MBA, M.Com, M.A., M.Sc., M.Des, M.Pharm, and Ph.D. courses across multiple disciplines. The university emphasizes multidisciplinary education and industry readiness. Placements are moderate, with 70% of students placed at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 23,\n            \"name\": \"Dayananda Sagar Academy of Technology and Management\",\n            \"acronym\": \"DSATM\",\n            \"ranking\": 23,\n            \"address\": \"DSATM, Udayapura, Kanakapura Road, Bengaluru, Karnataka - 560082\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8756\\xb0 N, 77.5389\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: B.Arch: Architecture, MBA: General\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, L&T, Infosys, Wipro; highest package ~₹30 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for practical training with modest research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kanakapura Road; Konanakunte Cross Metro Station (Green Line), ~5 km, requiring auto or bus.\",\n            \"summary\": \"Dayananda Sagar Academy of Technology and Management (DSATM), established in 2011 by the Mahatma Gandhi Vidya Peetha Trust, is a VTU-affiliated autonomous college in Udayapura, South Bangalore. Its 10-acre campus offers six B.E. programs, two M.Tech specializations, B.Arch, and MBA courses, enrolling around 2,500 students. DSATM focuses on core and IT engineering alongside architecture, gaining autonomy in 2018. Placements are decent, with 80% of students placed in 2023 at firms like TCS and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSATM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2011,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 24,\n            \"name\": \"Dayananda Sagar University\",\n            \"acronym\": \"DSU\",\n            \"ranking\": 24,\n            \"address\": \"Devarakaggalahalli, Harohalli, Kanakapura Road, Ramanagara Dt., Bengaluru – 562 112\",\n            \"locationUrl\": \"https://www.google.co.in/maps/dir//Devarakaggalahalli,+Harohalli+Kanakapura+Road,+Dt,+Ramanagara,+Karnataka+562112/@12.6606565,77.368438,12z/data=!4m8!4m7!1m0!1m5!1m1!1s0x3bae5b32ad06ec57:0x95e7a57b8a6b94d2!2m2!1d77.4508399!2d12.6606692?entry=ttu&g_ep=EgoyMDI1MDMyMy4wIKXMDSoASAFQAw%3D%3D\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), Electronics and Communication Engineering, Mechanical Engineering, Artificial learning and data science, Computer science and engineering (data science), Robotics and artificial engineering, CSE&ME, Computer science and engineering (cyber security), Aerospace Engineering, Data Sciences - BBA: General - B.Com: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems - MBA: Finance, Marketing, HR - M.Pharm: Pharmacology - **Other Programs**: Ph.D.: Engineering, Management, Pharmacy\",\n            \"placementDetails\": \"85% placement rate; companies include Amazon, Infosys, Deloitte, TCS; highest package ~₹35 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"130-acre campus with advanced classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for AI, Biotech, Aerospace, and Pharmacy supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"Dayananda Sagar University (DSU), established in 2014 by the Mahatma Gandhi Vidya Peetha Trust, is a private university in Kumaraswamy Layout, South Bangalore. Its sprawling 130-acre campus offers six B.Tech programs, BBA, B.Com, B.Pharm, M.Tech, MBA, M.Pharm, and Ph.D. courses, serving over 5,000 students. DSU's modern curriculum includes Aerospace and Data Sciences, reflecting industry trends. Labs drive innovation, and placements are strong, with 85% of students placed in 2023 at firms like Amazon and Infosys, peaking at ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2014,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"130 acres\"\n        },\n        {\n            \"id\": 25,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 25,\n            \"address\": \"AIT, Acharya Dr. Sarvepalli Radhakrishnan Road, Soladevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0836\\xb0 N, 77.4819\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning,  Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Mechatronics, Mining Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Science and Engineering, Digital Communication Engineering, Machine Design - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹30 LPA (2023). Balanced placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Mechatronics, and Mining supporting research and training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soladevanahalli Road; Chikkabanavara Metro Station (upcoming Pink Line, ~5 km), currently reliant on buses.\",\n            \"summary\": \"Acharya Institute of Technology (AIT), established in 2000 by the Acharya Institutes Group, is a VTU-affiliated autonomous college in Soladevanahalli, North Bangalore. Its 120-acre campus offers 12 B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses, serving over 5,000 students. AIT focuses on diverse engineering fields like Aeronautical and Mining Engineering, gaining autonomy in 2017. Placements are strong, with 85% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 26,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 26,\n            \"address\": \"Presidency University, Itgalpur, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1717\\xb0 N, 77.6118\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (Artificial Intelligence & Machine Learning), Civil Engineering, Electronics and Communication Engineering, Mechanical Engineering, Petroleum Engineering, Data Science, Cybersecurity - BBA: General, Digital Marketing, Business Analytics - B.Com: General, Accounting and Taxation - B.Des: Product Design, Communication Design - B.A. LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems, Data Sciences - MBA: Finance, Marketing, Human Resources, Business Analytics - LLM: Intellectual Property Rights - **Other Programs**: Ph.D.: Engineering, Management, Law, Sciences\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, Capgemini, EY, TCS; highest package ~₹35 LPA (2023). Focus on IT and management roles.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"64-acre campus with modern classrooms, library, hostels (separate for boys and girls), sports complex (cricket, football, basketball), gym, auditorium, multi-cuisine cafeterias.\",\n            \"labs\": \"Advanced labs for AI, Civil Engineering, Electronics, Petroleum Engineering, and Cybersecurity equipped with industry-standard tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; Yelahanka Metro Station (Green Line), ~7 km, accessible by auto or college-provided transport; upcoming Pink Line will improve connectivity.\",\n            \"summary\": \"Presidency University, established in 2013 by the Presidency Group of Institutions, is a private university located in Itgalpur, Rajanakunte, near Yelahanka in North Bangalore. Spanning a 64-acre campus, it offers a diverse range of programs including B.Tech, BBA, B.Com, B.Des, integrated law, M.Tech, MBA, LLM, and Ph.D. courses, enrolling over 5,000 students annually. The university emphasizes multidisciplinary education, blending engineering with management, design, and law. Placements are solid, with 80% of students placed in 2023 at companies like Infosys, Capgemini, and EY, with a peak package of ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"64 acres\"\n        },\n        {\n            \"id\": 27,\n            \"name\": \"Nagarjuna College of Engineering and Technology\",\n            \"acronym\": \"NCET\",\n            \"ranking\": 27,\n            \"address\": \"NCET, 38/1, Mudugurki, Devanahalli, Bengaluru, Karnataka - 562164\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2335\\xb0 N, 77.7119\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Artificial engineering and machine learning,  Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Construction Technology, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, Mindtree, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements with IT focus.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, surrounded by rural greenery.\",\n            \"labs\": \"Labs for Civil Engineering, CSE, ECE, and Mechanical, equipped for practical training with basic research capabilities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Devanahalli; no direct metro, ~30 km from Bangalore city center, reliant on college transport or private vehicles.\",\n            \"summary\": \"Nagarjuna College of Engineering and Technology (NCET), established in 2001 by the Nagarjuna Education Society, is a VTU-affiliated autonomous college located in Mudugurki, Devanahalli, approximately 30 km from Bangalore near the Kempegowda International Airport. Spanning an expansive 100-acre campus, NCET offers five B.E. programs, three M.Tech specializations, MBA, and Ph.D. courses, catering to around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NCET\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 28,\n            \"name\": \"Sri Venkateswara College of Engineering\",\n            \"acronym\": \"SVCE\",\n            \"ranking\": 28,\n            \"address\": \"SVCE, NH-7, Vidyanagar, Kempegowda International Airport Road, Bengaluru, Karnataka - 562157\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1547\\xb0 N, 77.6138\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Artificial engineering and machine learning , Computer science and engineering (data science), Computer science and engineering (cyber security) ,Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: MBA: General\",\n            \"placementDetails\": \"70% placement rate; companies include Infosys, HCL, Tech Mahindra, TCS; highest package ~₹20 LPA (2023). Modest placements with IT focus.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"20-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a semi-rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for basic practical training with limited research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on NH-7; no direct metro, near Airport Road, ~25 km from Bangalore city center, reliant on college transport.\",\n            \"summary\": \"Sri Venkateswara College of Engineering (SVCE), established in 2001 by the Sri Venkateswara Educational and Charitable Trust, is a VTU-affiliated college located in Vidyanagar, along NH-7 near Kempegowda International Airport, about 25 km from Bangalore's core. Its 20-acre campus offers six B.E. programs, two M.Tech specializations, and an MBA, serving approximately 1,500 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SVCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"20 acres\"\n        },\n        {\n            \"id\": 29,\n            \"name\": \"SJ College of Engineering (SJCE Mysore)\",\n            \"acronym\": \"SJCE\",\n            \"ranking\": 29,\n            \"address\": \"JSS Science and Technology University (SJCE), JSS TI Campus, Manasagangothri, Mysuru, Karnataka - 570006 (Not Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3135\\xb0 N, 76.6738\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Computer Science and Business Systems, Construction Technology and Management, Electrical and Electronics Engineering, Artificial engineering and machine learning ,Electronics and Communication Engineering, Environmental Engineering, Industrial and Production Engineering, Information Science and Engineering, Mechanical Engineering, Polymer Science and Technology - **Postgraduate (M.Tech)**: Automotive Electronics, Biotechnology, Computer Engineering, Energy Systems and Management, Environmental Engineering, Industrial Electronics, Maintenance Engineering, Software Engineering, Structural Engineering - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering, Management, Sciences\",\n            \"placementDetails\": \"90% placement rate; companies include Infosys, Cisco, L&T, TCS; highest package ~₹40 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"117-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, auditorium, and dining facilities.\",\n            \"labs\": \"Labs for Biotech, Robotics, Structural Engineering, and Polymer Science, equipped for advanced research and training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses serve Mysuru; no metro in Mysuru, ~120 km from Bangalore, requiring intercity travel.\",\n            \"summary\": \"JSS Science and Technology University (SJCE), originally established as Sri Jayachamarajendra College of Engineering in 1963 by the JSS Mahavidyapeetha, is a premier autonomous institution in Mysuru, Karnataka, approximately 120 km from Bangalore. Transitioning to university status in 2016, its 117-acre campus in Manasagangothri offers 12 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses, enrolling over 4,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"117 acres\"\n        },\n        {\n            \"id\": 30,\n            \"name\": \"SJC Institute of Technology\",\n            \"acronym\": \"SJCIT\",\n            \"ranking\": 30,\n            \"address\": \"SJCIT, P.B. No. 20, B.B. Road, Chickballapur, Karnataka - 562101 (Near Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.4351\\xb0 N, 77.7315\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning , Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Communication and Networking, Machine Design, Structural Engineering - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"53-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training with basic research scope.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Chickballapur; no direct metro, ~50 km from Bangalore, reliant on college transport or private vehicles.\",\n            \"summary\": \"SJC Institute of Technology (SJCIT), established in 1986 by the Sri Adichunchanagiri Shikshana Trust, is a VTU-affiliated college located in Chickballapur, about 50 km north of Bangalore. Its expansive 53-acre campus offers six B.E. programs, four M.Tech specializations, MBA, and Ph.D. courses, serving around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"53 acres\"\n        },\n        {\n            \"id\": 31,\n            \"name\": \"MVJ College of Engineering\",\n            \"acronym\": \"MVJCE\",\n            \"ranking\": 31,\n            \"address\": \"MVJCE, Near ITPB, Whitefield, Bengaluru, Karnataka - 560067\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, Infosys, Wipro, Accenture; highest package ~₹28 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 28,\n            \"infrastructure\": \"15-acre campus with modern classrooms, library, hostels, sports facilities (cricket, basketball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and VLSI, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Whitefield Road; Whitefield Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"MVJ College of Engineering (MVJCE), established in 1982 by the Venkatesha Education Society, is a VTU-affiliated autonomous college in Whitefield, East Bangalore. Its 15-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses, serving around 2,500 students. MVJCE focuses on IT and core engineering, with a practical curriculum enhanced by autonomy since 2015. Placements are reliable, with 80% of students placed in 2023 at firms like TCS and Infosys, peaking at ₹28 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MVJCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1982,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"15 acres\"\n        },\n        {\n            \"id\": 32,\n            \"name\": \"Bapuji Institute of Engineering and Technology\",\n            \"acronym\": \"BIET\",\n            \"ranking\": 33,\n            \"address\": \"BIET, Shamanur Road, Davangere, Karnataka - 577004 (Not Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"14.4673\\xb0 N, 75.9218\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design, Power Electronics, Structural Engineering - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Infosys, L&T, Wipro; highest package ~₹35 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"41-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens in Davangere.\",\n            \"labs\": \"Labs for Civil, Mechanical, ECE, and CSE, equipped for practical training and research activities.\",\n            \"busAndMetroConvenience\": \"KSRTC buses serve Davangere; no metro in Davangere, ~260 km from Bangalore.\",\n            \"summary\": \"Bapuji Institute of Engineering and Technology (BIET), established in 1979 in Davangere, is one of Karnataka's respected VTU-affiliated autonomous colleges. Its 41-acre campus offers six B.E. programs, four M.Tech specializations, MBA, MCA, and Ph.D. courses, enrolling over 2,500 students. BIET focuses on Civil and Mechanical Engineering, expanding to modern IT fields. Placements are excellent, with 85% of students placed in 2023 at firms like TCS and L&T, peaking at ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIET\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1979,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"41 acres\"\n        },\n        {\n            \"id\": 33,\n            \"name\": \"Alliance College of Engineering\",\n            \"acronym\": \"ACE\",\n            \"ranking\": 34,\n            \"address\": \"Alliance University, Chikkahagade Cross, Chandapura-Anekal Main Road, Bengaluru, Karnataka - 562106\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design; MBA: General - **Other Programs**: Ph.D.: Engineering, Management\",\n            \"placementDetails\": \"75% placement rate; companies include Infosys, TCS, Wipro, Capgemini; highest package ~₹22 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"60-acre campus with modern classrooms, library, hostels, sports complex (cricket, football), gym, and dining facilities.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Chandapura Road; Electronic City Metro Station (upcoming Yellow Line), ~10 km, currently reliant on buses.\",\n            \"summary\": \"Alliance College of Engineering (ACE), established in 2010 as part of Alliance University, is located in Chandapura, South Bangalore. Its 60-acre campus offers four B.Tech programs, two M.Tech specializations, MBA, and Ph.D. courses, serving around 1,500 students. ACE emphasizes practical learning and industry exposure. Placements are decent, with 75% of students placed in 2023 at firms like Infosys and TCS, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=ACE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2010,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"60 acres\"\n        },\n        {\n            \"id\": 34,\n            \"name\": \"Sapthagiri NPS University\",\n            \"acronym\": \"SNPU\",\n            \"ranking\": 35,\n            \"address\": \"Sapthagiri College of Engineering, #14/5, Chikkasandra, Hesaraghatta Main Road, Bengaluru, Karnataka - 560057\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0856\\xb0 N, 77.5194\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹20 LPA (2023). Basic mid-tier placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"10-acre compact campus with classrooms, library, hostels, sports area (volleyball, badminton), and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; Jalahalli Metro Station (Green Line), ~8 km, requiring auto or bus.\",\n            \"summary\": \"Sapthagiri NPS University (SNPU), established in 2001, is a private institution in Chikkasandra, North Bangalore. Its compact 10-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,200 students. SNPU focuses on basic engineering education with limited research scope. Placements are modest, with 70% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SNPU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 35,\n            \"name\": \"Atria Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 36,\n            \"address\": \"ATRIA, Off Magadi Road, Vishwaneedam Post, Bengaluru, Karnataka - 560091\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Mindtree; highest package ~₹22 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"20-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Magadi Road; Nayandahalli Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Atria Institute of Technology (AIT), established in 2001 by the Atria Educational Trust, is a VTU-affiliated college in Vishwaneedam, West Bangalore. Its 20-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,800 students. AIT focuses on core engineering and IT, with a practical curriculum. Placements are decent, with 75% of students placed in 2023 at firms like TCS and Infosys, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"20 acres\"\n        },\n        {\n            \"id\": 36,\n            \"name\": \"Jain University\",\n            \"acronym\": \"JU\",\n            \"ranking\": 37,\n            \"address\": \" 45th km, NH - 209,Jakkasandra Post, Bengaluru - Kanakapura Rd, Bengaluru, Karnataka 562112)\",\n            \"locationUrl\": \" https://www.google.co.in/maps/place/JAIN+(Deemed-to-be-University),+Faculty+of+Engineering+and+Technology+(FET)/@12.6421763,77.4399964,17z/data=!3m1!4b1!4m6!3m5!1s0x3bae5ba739694f47:0x424bdd92f039db75!8m2!3d12.6421763!4d77.4399964!16s%2Fg%2F11crxs1th4?entry=ttu&g_ep=EgoyMDI1MDMyMy4wIKXMDSoASAFQAw%3D%3D \",\n            \"coordinates\": \"12.6421763\\xb0 N, 77.4399964\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering, Electrical and Electronics Engineering, Artificial Intelligence and Machine Learning, Data Science, Cybersecurity, Biotechnology, Chemical Engineering - B.Arch: Architecture - BBA: General - B.Com: General - B.Des: Product Design, Fashion Design - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design, Data Science, Artificial Intelligence - MBA: Finance, Marketing, HR, Business Analytics - M.Arch: Architecture - M.Des: Product Design - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts\",\n            \"placementDetails\": \"85% placement rate; companies include Microsoft, Amazon, Google, TCS, Infosys, Wipro, Accenture, IBM; highest package ~₹55 LPA (2023). Strong tech and consulting placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 55,\n            \"infrastructure\": \"Multiple campuses with modern classrooms, libraries, hostels, sports complexes (cricket, football, basketball), gyms, medical centers, and dining facilities across Bangalore.\",\n            \"labs\": \"State-of-the-art labs for AI, Robotics, VLSI, Biotechnology, Chemical Engineering, and advanced computing facilities with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kanakapura Road; Vajarahalli Metro Station (Purple Line), ~10 km, accessible by auto or college transport.\",\n            \"summary\": \"Jain University, established in 1990 and granted university status in 2009, is a leading private deemed university with multiple campuses across Bangalore. Known for its diverse academic offerings, Jain University serves over 20,000 students across engineering, management, arts, and sciences. The Faculty of Engineering and Technology (FET) offers 10 B.Tech programs, 4 M.Tech specializations, and strong industry partnerships. Ranked 68 in NIRF 2024 (University), it emphasizes innovation, research, and entrepreneurship. Placements are excellent, with 85% of students placed at top firms like Microsoft and Amazon, peaking at ₹55 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=JU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1990,\n            \"nirf\": \"68\",\n            \"campusSize\": \"Multiple Campuses\"\n        },\n        {\n            \"id\": 37,\n            \"name\": \"Oxford College of Engineering\",\n            \"acronym\": \"OCE\",\n            \"ranking\": 38,\n            \"address\": \"Oxford College, 10th Milestone, Bommanahalli, Hosur Road, Bengaluru, Karnataka - 560068\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Mindtree; highest package ~₹18 LPA (2023). Basic mid-tier placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"6-acre compact urban campus with classrooms, library, hostels, sports area (volleyball, badminton), and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Bommanahalli Metro Station (Green Line), ~2 km, easily accessible.\",\n            \"summary\": \"Oxford College of Engineering (OCE), established in 1974, is one of Bangalore's older private engineering colleges located on Hosur Road. Its compact 6-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,500 students. OCE focuses on basic engineering education with limited research scope. Placements are modest, with 70% of students placed in 2023 at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=OCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1974,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"6 acres\"\n        },\n        {\n            \"id\": 38,\n            \"name\": \"Sri Krishna Institute of Technology\",\n            \"acronym\": \"SKIT\",\n            \"ranking\": 39,\n            \"address\": \"SKIT, #29, Hesaraghatta Main Road, Chimney Hills, Chikkabanavara, Bengaluru, Karnataka - 560090\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0856\\xb0 N, 77.5194\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"65% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹15 LPA (2023). Basic placements.\",\n            \"placementRate\": 65,\n            \"highestPackage\": 15,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; Jalahalli Metro Station (Green Line), ~8 km, requiring auto or bus.\",\n            \"summary\": \"Sri Krishna Institute of Technology (SKIT), established in 2007, is a VTU-affiliated college in Chikkabanavara, North Bangalore. Its 10-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,200 students. SKIT focuses on basic engineering education. Placements are modest, with 65% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹15 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SKIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2007,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 39,\n            \"name\": \"Don Bosco Institute of Technology\",\n            \"acronym\": \"DBIT\",\n            \"ranking\": 40,\n            \"address\": \"Don Bosco Institute, Mysore Road, Kumbalagodu, Bengaluru, Karnataka - 560074\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Mindtree; highest package ~₹20 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"36-acre campus with classrooms, library, hostels, sports facilities (cricket, football), gym, chapel, and dining halls.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysore Road; Kengeri Metro Station (Purple Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Don Bosco Institute of Technology (DBIT), established in 2001 by the Salesians of Don Bosco, is a VTU-affiliated college in Kumbalagodu, South Bangalore. Its 36-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,800 students. DBIT emphasizes holistic education with strong values. Placements are decent, with 70% of students placed in 2023 at firms like TCS and Infosys, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DBIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"36 acres\"\n        },\n        {\n            \"id\": 40,\n            \"name\": \"Sambhram Institute of Technology\",\n            \"acronym\": \"SaIT\",\n            \"ranking\": 41,\n            \"address\": \"SaIT, M.S. Palya, Via Jalahalli East, Bengaluru, Karnataka - 560097\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0856\\xb0 N, 77.5194\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"65% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹18 LPA (2023). Basic placements.\",\n            \"placementRate\": 65,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports area (volleyball, badminton), and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Jalahalli Road; Jalahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"Sambhram Institute of Technology (SaIT), established in 2001, is a VTU-affiliated college in M.S. Palya, North Bangalore. Its 10-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,200 students. SaIT focuses on basic engineering education. Placements are modest, with 65% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SaIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 41,\n            \"name\": \"Brindavan College of Engineering\",\n            \"acronym\": \"BCE\",\n            \"ranking\": 42,\n            \"address\": \"Brindavan College, Dwarakanagar, Bagalur Main Road, Yelahanka, Bengaluru, Karnataka - 560063\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"60% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹15 LPA (2023). Basic placements.\",\n            \"placementRate\": 60,\n            \"highestPackage\": 15,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports area (volleyball, badminton), and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Bagalur Road; Yelahanka Metro Station (Green Line), ~8 km, requiring auto or bus.\",\n            \"summary\": \"Brindavan College of Engineering (BCE), established in 1993, is a VTU-affiliated college in Dwarakanagar, North Bangalore. Its 10-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,000 students. BCE focuses on basic engineering education. Placements are modest, with 60% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹15 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1993,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 42,\n            \"name\": \"Impact College of Engineering\",\n            \"acronym\": \"ICE\",\n            \"ranking\": 43,\n            \"address\": \"Impact College, Sahakara Nagar, Kodigehalli, Bengaluru, Karnataka - 560092\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0856\\xb0 N, 77.5194\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"55% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹12 LPA (2023). Basic placements.\",\n            \"placementRate\": 55,\n            \"highestPackage\": 12,\n            \"infrastructure\": \"5-acre compact campus with classrooms, library, hostels, sports area (volleyball), and canteens.\",\n            \"labs\": \"Basic labs for CSE, ECE, and Mechanical, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Sahakara Nagar; Jalahalli Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Impact College of Engineering (ICE), established in 1986, is a VTU-affiliated college in Sahakara Nagar, North Bangalore. Its compact 5-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 800 students. ICE focuses on basic engineering education. Placements are modest, with 55% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹12 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=ICE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 43,\n            \"name\": \"AMC College of Engineering\",\n            \"acronym\": \"AMCCE\",\n            \"ranking\": 44,\n            \"address\": \"AMC Engineering College, 18th K.M., Bannerghatta Main Road, Bengaluru, Karnataka - 560083\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"60% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹15 LPA (2023). Basic placements.\",\n            \"placementRate\": 60,\n            \"highestPackage\": 15,\n            \"infrastructure\": \"52-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Bannerghatta Road; Jayadeva Metro Station (Green Line), ~8 km, requiring auto or bus.\",\n            \"summary\": \"AMC College of Engineering (AMCCE), established in 1999, is a VTU-affiliated college on Bannerghatta Road, South Bangalore. Its 52-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,500 students. AMCCE focuses on basic engineering education. Placements are modest, with 60% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹15 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AMCCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1999,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"52 acres\"\n        },\n        {\n            \"id\": 44,\n            \"name\": \"Cambridge Institute of Technology\",\n            \"acronym\": \"CIT\",\n            \"ranking\": 45,\n            \"address\": \"CIT, K.R. Puram, Bengaluru, Karnataka - 560036\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Mindtree; highest package ~₹18 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on K.R. Puram; Baiyyappanahalli Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"Cambridge Institute of Technology (CIT), established in 2007, is a VTU-affiliated college in K.R. Puram, East Bangalore. Its 10-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,200 students. Ranked 151-200 in NIRF 2024 (Engineering), CIT focuses on practical engineering education. Placements are decent, with 70% of students placed in 2023 at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=CIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2007,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 45,\n            \"name\": \"T. John Institute of Technology\",\n            \"acronym\": \"TJIT\",\n            \"ranking\": 46,\n            \"address\": \"TJIT, #86/1, Gottigere, Bannerghatta Road, Bengaluru, Karnataka - 560083\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering - **Other Programs**: MBA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"65% placement rate; companies include TCS, Wipro, Infosys; highest package ~₹16 LPA (2023). Basic placements.\",\n            \"placementRate\": 65,\n            \"highestPackage\": 16,\n            \"infrastructure\": \"20-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Bannerghatta Road; Jayadeva Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"T. John Institute of Technology (TJIT), established in 2006, is a VTU-affiliated college in Gottigere, South Bangalore. Its 20-acre campus offers five B.E. programs, one M.Tech specialization, MBA, and Ph.D. courses, serving around 1,200 students. TJIT focuses on basic engineering education. Placements are modest, with 65% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹16 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=TJIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2006,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"20 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map((param)=>{\n        let [range, count] = param;\n        return {\n            range,\n            count\n        };\n    });\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b - a;\n    }).slice(0, 10).map((param)=>{\n        let [company, mentions] = param;\n        return {\n            company,\n            mentions\n        };\n    });\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return \"₹\".concat((amount / 100).toFixed(2), \" Cr\");\n    }\n    return \"₹\".concat(amount, \" LPA\");\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return \"\".concat(size, \" acres\");\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : \"NIRF \".concat(nirf);\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = function() {\n    let collegeName = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n    const message = collegeName ? \"Hi! I'm interested in learning more about \".concat(collegeName, \" and would like a free consultation.\") : \"Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?\";\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodeURIComponent(message));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/collegeData.js\n"));

/***/ })

});