/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/compare/page";
exports.ids = ["app/compare/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcompare%2Fpage&page=%2Fcompare%2Fpage&appPaths=%2Fcompare%2Fpage&pagePath=private-next-app-dir%2Fcompare%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcompare%2Fpage&page=%2Fcompare%2Fpage&appPaths=%2Fcompare%2Fpage&pagePath=private-next-app-dir%2Fcompare%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'compare',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/compare/page.js */ \"(rsc)/./src/app/compare/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/compare/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/compare/page\",\n        pathname: \"/compare\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcompare%2Fpage&page=%2Fcompare%2Fpage&appPaths=%2Fcompare%2Fpage&pagePath=private-next-app-dir%2Fcompare%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.js */ \"(ssr)/./src/components/Header.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDc3JjJTVDY29tcG9uZW50cyU1Q0hlYWRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3VzZXIlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDZWR1Y2F0aW9uJTIwMSU1Q3NyYyU1Q3N0eWxlcyU1Q2dsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBeUk7QUFDekkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iYW5nYWxvcmUtZW5naW5lZXJpbmctY29sbGVnZXMvPzIxNTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGVkdWNhdGlvbiAxXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZWR1Y2F0aW9uIDFcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcSGVhZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Ccomponents%5CHeader.js&modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Cstyles%5Cglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Ccompare%5Cpage.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Ccompare%5Cpage.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/compare/page.js */ \"(ssr)/./src/app/compare/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNlZHVjYXRpb24lMjAxJTVDc3JjJTVDYXBwJTVDY29tcGFyZSU1Q3BhZ2UuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmFuZ2Fsb3JlLWVuZ2luZWVyaW5nLWNvbGxlZ2VzLz8zZGNhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxlZHVjYXRpb24gMVxcXFxzcmNcXFxcYXBwXFxcXGNvbXBhcmVcXFxccGFnZS5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp%5Ccompare%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/compare/page.js":
/*!*********************************!*\
  !*** ./src/app/compare/page.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ComparePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,MapPin,Plus,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,MapPin,Plus,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,MapPin,Plus,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _lib_collegeData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/collegeData */ \"(ssr)/./src/lib/collegeData.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ComparePage() {\n    const [allColleges, setAllColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedColleges, setSelectedColleges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllColleges((0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.getAllColleges)());\n    }, []);\n    const filteredColleges = allColleges.filter((college)=>college.name.toLowerCase().includes(searchQuery.toLowerCase()) || college.acronym.toLowerCase().includes(searchQuery.toLowerCase()));\n    const addCollege = (college)=>{\n        if (selectedColleges.length < 4 && !selectedColleges.find((c)=>c.id === college.id)) {\n            setSelectedColleges([\n                ...selectedColleges,\n                college\n            ]);\n            setShowAddModal(false);\n            setSearchQuery(\"\");\n        }\n    };\n    const removeCollege = (collegeId)=>{\n        setSelectedColleges(selectedColleges.filter((c)=>c.id !== collegeId));\n    };\n    const comparisonFields = [\n        {\n            key: \"ranking\",\n            label: \"Overall Ranking\",\n            format: (value)=>`#${value}`\n        },\n        {\n            key: \"nirf\",\n            label: \"NIRF Ranking\",\n            format: _lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.formatNIRF\n        },\n        {\n            key: \"establishedYear\",\n            label: \"Established\",\n            format: (value)=>value\n        },\n        {\n            key: \"placementRate\",\n            label: \"Placement Rate\",\n            format: (value)=>`${value}%`\n        },\n        {\n            key: \"highestPackage\",\n            label: \"Highest Package\",\n            format: _lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.formatCurrency\n        },\n        {\n            key: \"campusSize\",\n            label: \"Campus Size\",\n            format: (value)=>value\n        },\n        {\n            key: \"metroAccess\",\n            label: \"Metro Access\",\n            format: (value)=>value ? \"Yes\" : \"No\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-max py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Compare Engineering Colleges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Compare up to 4 colleges side by side to make an informed decision about your engineering education.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-max py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"Select Colleges to Compare (\",\n                                    selectedColleges.length,\n                                    \"/4)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                                children: [\n                                    selectedColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-4 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>removeCollege(college.id),\n                                                    className: \"absolute top-2 right-2 text-gray-400 hover:text-red-500 transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 text-sm mb-1\",\n                                                            children: college.acronym\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 line-clamp-2\",\n                                                            children: college.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-primary-100 text-primary-600 px-2 py-1 rounded\",\n                                                            children: [\n                                                                \"#\",\n                                                                college.ranking\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600 font-semibold\",\n                                                            children: [\n                                                                college.placementRate,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, college.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)),\n                                    selectedColleges.length < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddModal(true),\n                                        className: \"card p-4 border-2 border-dashed border-gray-300 hover:border-primary-500 hover:bg-primary-50 transition-all duration-200 flex flex-col items-center justify-center text-gray-500 hover:text-primary-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-8 w-8 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Add College\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    selectedColleges.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-6 mb-8 overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"text-left py-3 px-4 font-semibold text-gray-900 border-b\",\n                                                    children: \"Criteria\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                selectedColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center py-3 px-4 font-semibold text-gray-900 border-b min-w-[200px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-bold\",\n                                                                    children: college.acronym\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                                    lineNumber: 119,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 font-normal\",\n                                                                    children: college.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                                    lineNumber: 120,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, college.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: comparisonFields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: index % 2 === 0 ? \"bg-gray-50\" : \"bg-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"py-4 px-4 font-medium text-gray-900\",\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedColleges.map((college)=>{\n                                                        const value = college[field.key];\n                                                        const formattedValue = field.format(value);\n                                                        // Highlight best values\n                                                        let isHighlighted = false;\n                                                        if (field.key === \"ranking\") {\n                                                            isHighlighted = value === Math.min(...selectedColleges.map((c)=>c[field.key]));\n                                                        } else if (field.key === \"placementRate\" || field.key === \"highestPackage\") {\n                                                            isHighlighted = value === Math.max(...selectedColleges.map((c)=>c[field.key]));\n                                                        } else if (field.key === \"metroAccess\") {\n                                                            isHighlighted = value === true;\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-4 px-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-block px-3 py-1 rounded-full text-sm font-medium ${isHighlighted ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                                                children: formattedValue\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, college.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, field.key, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    selectedColleges.length >= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-6\",\n                                        children: \"Placement Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-6\",\n                                        children: selectedColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-l-4 border-primary-500 pl-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: college.acronym\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 text-sm leading-relaxed\",\n                                                        children: college.placementDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, college.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-6\",\n                                        children: \"Infrastructure & Facilities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-6\",\n                                        children: selectedColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-l-4 border-secondary-500 pl-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: college.acronym\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 text-sm leading-relaxed mb-3\",\n                                                        children: college.infrastructure\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-3 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-medium text-gray-900 mb-1\",\n                                                                children: \"Laboratory Facilities:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-700 text-sm\",\n                                                                children: college.labs\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, college.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-6\",\n                                        children: \"Transportation & Connectivity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-6\",\n                                        children: selectedColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-l-4 border-orange-500 pl-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: college.acronym\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 text-sm leading-relaxed\",\n                                                        children: college.busAndMetroConvenience\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, college.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    selectedColleges.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary-600 text-white rounded-xl p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Need Help Making a Decision?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-100 mb-6 max-w-2xl mx-auto\",\n                                children: \"Get personalized guidance from our education experts to choose the best college for your career goals.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: (0,_lib_collegeData__WEBPACK_IMPORTED_MODULE_2__.getWhatsAppLink)(`I need help comparing these colleges: ${selectedColleges.map((c)=>c.acronym).join(\", \")}`),\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Get Expert Guidance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    selectedColleges.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: \"Start Comparing Colleges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Add at least 2 colleges to see a detailed comparison.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddModal(true),\n                                className: \"btn-primary\",\n                                children: \"Add Your First College\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Add College to Compare\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddModal(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_MapPin_Plus_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search colleges...\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-96\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: filteredColleges.filter((college)=>!selectedColleges.find((c)=>c.id === college.id)).slice(0, 10).map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>addCollege(college),\n                                        className: \"w-full text-left p-4 border border-gray-200 rounded-lg hover:border-primary-500 hover:bg-primary-50 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: college.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: college.acronym\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-semibold text-primary-600\",\n                                                            children: [\n                                                                \"#\",\n                                                                college.ranking\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                college.placementRate,\n                                                                \"% placement\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                            lineNumber: 289,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, college.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                        lineNumber: 284,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\compare\\\\page.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/compare/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Menu,Search,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: \"Home\",\n            href: \"/\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"All Colleges\",\n            href: \"/colleges\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Compare\",\n            href: \"/compare\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Rankings\",\n            href: \"/colleges?sort=ranking\",\n            icon: _barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-lg sticky top-0 z-40 border-b border-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-max\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-primary-600 to-secondary-600 p-2 rounded-xl group-hover:scale-110 transition-transform duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Bangalore Engineering\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"College Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center space-x-2 text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-4 w-4 group-hover:scale-110 transition-transform duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 45,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"btn-primary text-sm\",\n                                children: \"Free Consultation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 rounded-lg text-gray-700 hover:text-primary-600 hover:bg-gray-100 transition-colors duration-200\",\n                            \"aria-label\": \"Toggle menu\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Menu_Search_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-gray-100 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center space-x-3 text-gray-700 hover:text-primary-600 font-medium py-2 px-4 rounded-lg hover:bg-gray-50 transition-all duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn-primary w-full text-center text-sm\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"Free Consultation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Header.js\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: () => (/* binding */ colleges),\n/* harmony export */   filterColleges: () => (/* binding */ filterColleges),\n/* harmony export */   formatCampusSize: () => (/* binding */ formatCampusSize),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNIRF: () => (/* binding */ formatNIRF),\n/* harmony export */   getAggregateStats: () => (/* binding */ getAggregateStats),\n/* harmony export */   getAllColleges: () => (/* binding */ getAllColleges),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getFeaturedColleges: () => (/* binding */ getFeaturedColleges),\n/* harmony export */   getPlacementStats: () => (/* binding */ getPlacementStats),\n/* harmony export */   getTopCompanies: () => (/* binding */ getTopCompanies),\n/* harmony export */   getWhatsAppLink: () => (/* binding */ getWhatsAppLink),\n/* harmony export */   searchColleges: () => (/* binding */ searchColleges),\n/* harmony export */   sortColleges: () => (/* binding */ sortColleges)\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if (false) {}\n    return collegesData || getFallbackData();\n};\n// Complete dataset from college.json - All 50 colleges\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"MS Ramaiah Institute of Technology\",\n            \"acronym\": \"MSRIT\",\n            \"ranking\": 6,\n            \"address\": \"MS Ramaiah Institute of Technology, MSR Nagar, MSRIT Post, Bengaluru, Karnataka - 560054.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0306\\xb0 N, 77.5653\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, , Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Integrated Manufacturing, Computer Science and Engineering, Digital Communication, Digital Electronics and Communication, Industrial Engineering, Manufacturing Science and Engineering, Software Engineering, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: B.Arch: Architecture; MBA: General; MCA: General; Ph.D.: All engineering disciplines, Architecture, Management.\",\n            \"placementDetails\": \"95% placement rate; companies include Amazon, Capgemini, Intel, TCS; highest package ~₹50 LPA (2023). Excellent tech and core placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 50,\n            \"infrastructure\": \"25-acre campus with smart classrooms, central library, hostels, sports complex (cricket, volleyball), gym, auditorium, and dining halls.\",\n            \"labs\": \"Advanced labs for AI, VLSI, Structural Engineering, Biotech, and Mechanical Engineering, equipped for research and industry projects.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve MSR Nagar; Sandal Soap Factory Metro Station (Green Line), ~2 km, accessible by auto or walk.\",\n            \"summary\": \"MSRIT, founded in 1962 by Dr. M.S. Ramaiah, spans 25 acres in North Bangalore. It offers 12 B.E. programs, 12 M.Tech specializations, and strong placements at firms like Amazon and Intel, peaking at ₹50 LPA in 2023. Labs support cutting-edge research, and connectivity includes BMTC buses and the Green Line metro.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1962,\n            \"nirf\": \"78\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"Sir M Visvesvaraya Institute of Technology\",\n            \"acronym\": \"Sir MVIT\",\n            \"ranking\": 7,\n            \"address\": \"Sir MVIT, Krishnadevaraya Nagar, Hunasamaranahalli, International Airport Road, Bengaluru, Karnataka - 562157.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1507\\xb0 N, 77.6082\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Computer science and engineering (cyber security and IoT) Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Integrated Manufacturing, Electronics, Mechanical Engineering (Design) - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines, Management.\",\n            \"placementDetails\": \"80%+ placement rate; companies include TCS, Wipro, Cognizant, Infosys; highest package ~₹30 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"133-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, auditorium, and canteens.\",\n            \"labs\": \"Labs for Electronics, Mechanical, Biotech, and Computer Science, supporting practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Airport Road; no direct metro, ~20 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Sir MVIT, established in 1986, spans a vast 133-acre campus on International Airport Road. It offers eight B.E. programs and reliable placements at firms like TCS and Wipro, peaking at ₹30 LPA in 2023. While its rural location limits accessibility, the large campus provides ample space for expansion.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SirMVIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"133 acres\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"Bangalore Institute of Technology\",\n            \"acronym\": \"BIT\",\n            \"ranking\": 8,\n            \"address\": \"BIT, K.R. Road, V.V. Puram, Bengaluru, Karnataka - 560004.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9561\\xb0 N, 77.5762\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Dell, Accenture, TCS; highest package ~₹37 LPA (2023). Strong IT and core engineering focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 37,\n            \"infrastructure\": \"5-acre urban campus with classrooms, library, hostels, sports area (volleyball, badminton), auditorium, and canteens.\",\n            \"labs\": \"Labs for AI, VLSI, Civil, Mechanical, and Electronics, equipped for practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent K.R. Road; Chickpet Metro Station (Green Line), ~1.5 km, highly accessible.\",\n            \"summary\": \"BIT, founded in 1979 under the Vokkaligara Sangha, is a well-regarded VTU-affiliated college in central Bangalore. Its compact 5-acre campus hosts nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Placements are strong, with 85% of students placed in 2023 at firms like Infosys and Dell, peaking at ₹37 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Nitte Meenakshi Institute of Technology\",\n            \"acronym\": \"NMIT\",\n            \"ranking\": 9,\n            \"address\": \"NMIT, P.B. No. 6429, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Artificial engineering and machine learning, Artificial learning and data science, Computer Science and Engineering, Computer Science and Engineering (Business system), Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Data Sciences, Machine Design, Renewable Energy, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Microsoft, Infosys, Huawei, TCS; highest package ~₹40 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"23-acre campus with modern classrooms, library, hostels, sports facilities (cricket, tennis), gym, and dining halls.\",\n            \"labs\": \"Robotics Lab, Aerospace Lab, Data Science Lab, VLSI Lab, and Mechanical Workshop, supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve Yelahanka; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"NMIT, established in 2001 by the Nitte Education Trust, is an autonomous VTU-affiliated college in Yelahanka. Spanning 23 acres, it offers seven B.E. programs, six M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it emphasizes innovation and research, with over 50 patents filed.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NMIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"23 acres\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"PES University (Electronic City Campus)\",\n            \"acronym\": \"PESUECC\",\n            \"ranking\": 10,\n            \"address\": \"PES University, Electronic City Campus, Hosur Road, Electronic City, Bengaluru, Karnataka - 560100.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Artificial engineering and machine learning,  Electronics and Communication Engineering, Mechanical Engineering - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Automotive Engineering); MBA: Finance, Marketing, HR; MCA: General - **Other Programs**: Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Amazon, Intel, Flipkart, TCS; highest package ~₹60 LPA (2023). Excellent tech placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 60,\n            \"infrastructure\": \"50-acre campus with advanced classrooms, library, hostels, sports complex (football, basketball), gym, and cafeterias.\",\n            \"labs\": \"Labs for AI, Electronics, Automotive Engineering, and Software Development, equipped with industry-grade tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Electronic City Metro Station (upcoming Yellow Line, ~2 km), currently reliant on buses or autos.\",\n            \"summary\": \"PES University's Electronic City Campus, established in 2005, offers three B.Tech programs, three M.Tech specializations, MBA, MCA, and Ph.D. courses. Proximity to Electronic City enhances tech exposure. Ranked alongside its Ring Road counterpart in NIRF 2024 (101-150, University), this campus benefits from PES's legacy and alumni like Nishanth Ananthram (Google).\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESUECC\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2005,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 11,\n            \"name\": \"CMR Institute of Technology\",\n            \"acronym\": \"CMRIT\",\n            \"ranking\": 11,\n            \"address\": \"CMRIT, 132, AECS Layout, ITPL Main Road, Kundalahalli, Bengaluru, Karnataka - 560037.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9698\\xb0 N, 77.7496\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Capgemini, IBM, Infosys; highest package ~₹30 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"8-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and VLSI, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on ITPL Road; Kundalahalli Metro Station (Purple Line), ~2 km, easily accessible.\",\n            \"summary\": \"CMRIT, founded in 2000 by the CMR Jnanadhara Trust, is a VTU-affiliated autonomous college in East Bangalore. Its compact 8-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it focuses on industry readiness.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=CMRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"8 acres\"\n        },\n        {\n            \"id\": 12,\n            \"name\": \"Dayananda Sagar College of Engineering\",\n            \"acronym\": \"DSCE\",\n            \"ranking\": 12,\n            \"address\": \"DSCE, Shavige Malleshwara Hills, Kumaraswamy Layout, Bengaluru, Karnataka - 560078.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Artificial engineering and machine learning, Computer science and engineering (data science), Computer science and engineering (cyber security), Computer science and engineering (Internet of things and block chain technology), Computer science and Business system, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Instrumentation Technology, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bioinformatics, Computer Integrated Manufacturing, Computer Science and Engineering, Design Engineering, Digital Electronics and Communication, Highway Technology, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Accenture, Cognizant, L&T, Infosys; highest package ~₹40 LPA (2023). Balanced IT and core placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"29-acre campus with modern classrooms, library, hostels, sports arena (cricket, basketball), gym, and dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Civil, VLSI, and Mechanical, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"DSCE, established in 1979, is a leading VTU-affiliated autonomous institute in South Bangalore. Its expansive 29-acre campus offers 14 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it boasts modern infrastructure and robust placements at firms like Accenture and L&T, peaking at ₹40 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"29 acres\"\n        },\n        {\n            \"id\": 13,\n            \"name\": \"BMS Institute of Technology\",\n            \"acronym\": \"BMSIT\",\n            \"ranking\": 13,\n            \"address\": \"BMSIT, Doddaballapur Main Road, Avalahalli, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1351\\xb0 N, 77.5718\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Computer science and business system, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Wipro, Dell, TCS; highest package ~₹35 LPA (2023). Strong IT focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"21-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, and canteens.\",\n            \"labs\": \"Labs for AI, IoT, Electronics, and Mechanical, equipped for practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Doddaballapur Road; Yelahanka Metro Station (Green Line), ~6 km, requiring auto or bus.\",\n            \"summary\": \"BMSIT, established in 2002 as a sister institution to BMSCE, offers eight B.E. programs, two M.Tech specializations, MCA, and Ph.D. courses. Autonomous since 2016, it aligns with industry trends and offers a balanced environment with green spaces.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"21 acres\"\n        },\n        {\n            \"id\": 14,\n            \"name\": \"Reva University\",\n            \"acronym\": \"REVA\",\n            \"ranking\": 14,\n            \"address\": \"Reva University, Rukmini Knowledge Park, Kattigenahalli, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1167\\xb0 N, 77.6344\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Civil Engineering, Computer Science and Engineering, Computer Science and IT, Computer Science and system, Artificial learning and data science, Computer science and engineering (IoT & cyber security including blockchain technology), CSE (AI & ML), CSE (Cybersecurity), Electronics and Communication Engineering, Electronics and Computer  Engineering,  Mechanical Engineering, Bioelectronics, Robotics and Automation - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design, Power Electronics, Structural Engineering - MBA: Finance, Marketing, HR - MCA: General - M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts\",\n            \"placementDetails\": \"80% placement rate; companies include Amazon, TCS, IBM, Infosys; highest package ~₹45 LPA (2023). Strong tech placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"45-acre campus with modern classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Robotics, Cloud Computing, Civil Engineering, and VLSI equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kattigenahalli Road; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"Reva University, established in 2004 as Reva Institute of Technology and granted university status in 2012, is a private institution in Kattigenahalli, Yelahanka, North Bangalore. Its 45-acre campus offers eight B.Tech specializations, BBA, B.Arch, M.Tech, MBA, M.Des, and Ph.D. courses, enrolling over 15,000 students across disciplines. Reva emphasizes multidisciplinary education blending engineering with management and design, featuring programs like Cybersecurity and Robotics. Labs support innovation, and placements are excellent, with 80% of students placed at firms like Amazon and TCS, peaking at ₹45 LPA. Ranked 151-200 in NIRF 2024 (University), it focuses on employability and entrepreneurship.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=REVA\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2004,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"45 acres\"\n        },\n        {\n            \"id\": 15,\n            \"name\": \"MS Ramaiah University of Applied Sciences\",\n            \"acronym\": \"MSRUAS\",\n            \"ranking\": 15,\n            \"address\": \"MSRUAS, University House, Gnanagangothri Campus, New BEL Road, MSR Nagar, Bengaluru, Karnataka - 560054\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0309\\xb0 N, 77.5643\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Aerospace Engineering, Artificial engineering and machine learning , Information science and engineering, Mathematics and computing, Robotics, Automotive Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Mechanical Engineering - B.Des: Product Design, Fashion Design - BBA: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Aircraft Design, Automotive Electronics, Data Sciences, Structural Engineering - MBA: General - M.Des: Product Design - M.Pharm: Pharmaceutics - **Other Programs**: Ph.D.: Engineering, Design, Pharmacy, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, HCL, Bosch, TCS; highest package ~₹40 LPA (2023). Balanced tech and core placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"25-acre campus with advanced classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aerospace, Automotive, Data Sciences, and Pharmacy equipped for research and industry collaboration.\",\n            \"busAndMetroConvenience\": \"BMTC buses on New BEL Road; Sandal Soap Factory Metro Station (Green Line), ~2 km, easily accessible.\",\n            \"summary\": \"MS Ramaiah University of Applied Sciences (MSRUAS), established in 2013 under the Gokula Education Foundation, is located in MSR Nagar, North Bangalore. Its 25-acre campus offers seven B.Tech programs, B.Des, BBA, B.Pharm, M.Tech, MBA, M.Des, M.Pharm, and Ph.D. courses, serving over 5,000 students. MSRUAS emphasizes applied learning with niche programs like Aircraft Design and Automotive Engineering. Labs drive research, and placements are solid, with 85% of students placed at firms like Infosys and Bosch, peaking at ₹40 LPA. Connectivity is excellent, with BMTC buses and the Green Line metro station nearby.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRUAS\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 16,\n            \"name\": \"Siddaganga Institute of Technology\",\n            \"acronym\": \"SIT\",\n            \"ranking\": 16,\n            \"address\": \"SIT, B.H. Road, Tumakuru, Karnataka - 572103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.3409\\xb0 N, 77.1180\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹25 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Biotech, Chemical, Civil, and Electronics, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Tumakuru; no metro connectivity, ~70 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"SIT, established in 1963 by the Siddaganga Education Society, is located in Tumakuru, 70 km from Bangalore. Its sprawling 100-acre campus offers nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes discipline and values-based education. Placements are reliable, with 80% of students placed at firms like TCS and Infosys, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 17,\n            \"name\": \"JSS Science and Technology University\",\n            \"acronym\": \"JSSTU\",\n            \"ranking\": 17,\n            \"address\": \"JSSTU, JSS Technical Institutions Campus, Mysuru, Karnataka - 570006\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3375\\xb0 N, 76.6244\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.E.: Aeronautical Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics - B.Arch: Architecture - **Postgraduate**: M.Tech: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - MBA: General - M.Arch: Architecture - **Other Programs**: Ph.D.: Engineering, Architecture, Management\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, TCS, Wipro, L&T; highest package ~₹30 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"200-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, dining facilities.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Chemical, and Electronics, equipped for research and practical training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses within Mysuru; no metro connectivity, ~150 km from Bangalore, requiring private transport or buses.\",\n            \"summary\": \"JSS Science and Technology University (JSSTU), established in 1963 and granted university status in 2008, is located in Mysuru. Its expansive 200-acre campus offers 11 B.E. programs, B.Arch, M.Tech, MBA, M.Arch, and Ph.D. courses. Ranked 101-150 in NIRF 2024 (Engineering), it emphasizes research and innovation. Placements are strong, with 85% of students placed at firms like Infosys and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=JSSTU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"200 acres\"\n        },\n        {\n            \"id\": 18,\n            \"name\": \"Sapthagiri College of Engineering\",\n            \"acronym\": \"SCE\",\n            \"ranking\": 18,\n            \"address\": \"SCE, 14/5, Chikkasandra, Hesaraghatta Main Road, Bengaluru, Karnataka - 560057\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0833\\xb0 N, 77.5167\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹20 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"15-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, supporting practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hesaraghatta Road; no direct metro, ~15 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"SCE, established in 1999 by the Sapthagiri Educational Trust, is located in Chikkasandra, North Bangalore. Its 15-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it focuses on affordable quality education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹20 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1999,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"15 acres\"\n        },\n        {\n            \"id\": 19,\n            \"name\": \"Atria Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 19,\n            \"address\": \"AIT, Anandnagar, Hebbal, Bengaluru, Karnataka - 560024\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0358\\xb0 N, 77.5970\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, Accenture; highest package ~₹25 LPA (2023). Solid IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, badminton), gym, canteens.\",\n            \"labs\": \"Labs for CSE, VLSI, Electronics, and Mechanical, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hebbal Road; Hebbal Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"AIT, established in 2000 by the Atria Educational Trust, is located in Anandnagar, Hebbal, North Bangalore. Its compact 10-acre campus offers six B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2016, it emphasizes industry readiness. Placements are good, with 80% of students placed at firms like Infosys and TCS, peaking at ₹25 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 20,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT_Acharya\",\n            \"ranking\": 20,\n            \"address\": \"Acharya Institute of Technology, Acharya Dr. Sarvepalli Radhakrishnan Road, Soldevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1000\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Machine Design - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹22 LPA (2023). Mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 22,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, CSE, ECE, and Mechanical, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soldevanahalli Road; no direct metro, ~10 km from Peenya Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Acharya Institute of Technology, established in 2000 by the Acharya Institutes, is located in Soldevanahalli, North Bangalore. Its sprawling 120-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU, it emphasizes holistic education. Placements are moderate, with 75% of students placed at firms like TCS and Infosys, peaking at ₹22 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT_Acharya\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 21,\n            \"name\": \"New Horizon College of Engineering\",\n            \"acronym\": \"NHCE\",\n            \"ranking\": 21,\n            \"address\": \"NHCE, Near Marathahalli, Outer Ring Road, Bengaluru, Karnataka - 560103\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9591\\xb0 N, 77.7085\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, TCS, Wipro, IBM; highest package ~₹28 LPA (2023). Good IT placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 28,\n            \"infrastructure\": \"62-acre campus with modern classrooms, library, hostels, sports facilities (cricket, basketball), gym, cafeterias.\",\n            \"labs\": \"Labs for Aeronautics, VLSI, CSE, and Electronics, equipped for practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Outer Ring Road; Marathahalli Metro Station (Purple Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"NHCE, established in 1982 by the New Horizon Educational Trust, is located near Marathahalli on the Outer Ring Road. Its 62-acre campus offers seven B.E. programs, two M.Tech specializations, MBA, MCA, and Ph.D. courses. Affiliated with VTU and autonomous since 2007, it emphasizes innovation and entrepreneurship. Placements are good, with 80% of students placed at firms like Infosys and IBM, peaking at ₹28 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NHCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1982,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"62 acres\"\n        },\n        {\n            \"id\": 22,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 22,\n            \"address\": \"Presidency University, Itgalpura, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2167\\xb0 N, 77.5833\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, Mechanical Engineering, Civil Engineering, Electrical and Electronics Engineering; BBA: General; B.Com: General; B.A.: Various specializations; B.Sc.: Various specializations; B.Des: Product Design; B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Computer Science and Engineering, VLSI Design; MBA: General; M.Com: General; M.A.: Various specializations; M.Sc.: Various specializations; M.Des: Product Design; M.Pharm: Pharmacy - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Arts, Pharmacy\",\n            \"placementDetails\": \"70% placement rate; companies include TCS, Infosys, Wipro, Cognizant; highest package ~₹18 LPA (2023). Entry-level placements.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 18,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Pharmacy, supporting practical training and research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; no direct metro, ~15 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Presidency University, established in 2013, is a private university located in Itgalpura, Rajanakunte, North Bangalore. Its expansive 100-acre campus offers five B.Tech programs, BBA, B.Com, B.A., B.Sc., B.Des, B.Pharm, M.Tech, MBA, M.Com, M.A., M.Sc., M.Des, M.Pharm, and Ph.D. courses across multiple disciplines. The university emphasizes multidisciplinary education and industry readiness. Placements are moderate, with 70% of students placed at firms like TCS and Infosys, peaking at ₹18 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 23,\n            \"name\": \"Dayananda Sagar Academy of Technology and Management\",\n            \"acronym\": \"DSATM\",\n            \"ranking\": 23,\n            \"address\": \"DSATM, Udayapura, Kanakapura Road, Bengaluru, Karnataka - 560082\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8756\\xb0 N, 77.5389\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: B.Arch: Architecture, MBA: General\",\n            \"placementDetails\": \"80% placement rate; companies include TCS, L&T, Infosys, Wipro; highest package ~₹30 LPA (2023). Decent mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"10-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, canteens.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for practical training with modest research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kanakapura Road; Konanakunte Cross Metro Station (Green Line), ~5 km, requiring auto or bus.\",\n            \"summary\": \"Dayananda Sagar Academy of Technology and Management (DSATM), established in 2011 by the Mahatma Gandhi Vidya Peetha Trust, is a VTU-affiliated autonomous college in Udayapura, South Bangalore. Its 10-acre campus offers six B.E. programs, two M.Tech specializations, B.Arch, and MBA courses, enrolling around 2,500 students. DSATM focuses on core and IT engineering alongside architecture, gaining autonomy in 2018. Placements are decent, with 80% of students placed in 2023 at firms like TCS and L&T, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSATM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2011,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"10 acres\"\n        },\n        {\n            \"id\": 24,\n            \"name\": \"Dayananda Sagar University\",\n            \"acronym\": \"DSU\",\n            \"ranking\": 24,\n            \"address\": \"Devarakaggalahalli, Harohalli, Kanakapura Road, Ramanagara Dt., Bengaluru – 562 112\",\n            \"locationUrl\": \"https://www.google.co.in/maps/dir//Devarakaggalahalli,+Harohalli+Kanakapura+Road,+Dt,+Ramanagara,+Karnataka+562112/@12.6606565,77.368438,12z/data=!4m8!4m7!1m0!1m5!1m1!1s0x3bae5b32ad06ec57:0x95e7a57b8a6b94d2!2m2!1d77.4508399!2d12.6606692?entry=ttu&g_ep=EgoyMDI1MDMyMy4wIKXMDSoASAFQAw%3D%3D\",\n            \"coordinates\": \"12.9081\\xb0 N, 77.5666\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), Electronics and Communication Engineering, Mechanical Engineering, Artificial learning and data science, Computer science and engineering (data science), Robotics and artificial engineering, CSE&ME, Computer science and engineering (cyber security), Aerospace Engineering, Data Sciences - BBA: General - B.Com: General - B.Pharm: Pharmacy - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems - MBA: Finance, Marketing, HR - M.Pharm: Pharmacology - **Other Programs**: Ph.D.: Engineering, Management, Pharmacy\",\n            \"placementDetails\": \"85% placement rate; companies include Amazon, Infosys, Deloitte, TCS; highest package ~₹35 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"130-acre campus with advanced classrooms, library, hostels, sports complex (cricket, football), gym, dining facilities.\",\n            \"labs\": \"Labs for AI, Biotech, Aerospace, and Pharmacy supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Kumaraswamy Layout; Yelachenahalli Metro Station (Green Line), ~3 km, accessible by auto or bus.\",\n            \"summary\": \"Dayananda Sagar University (DSU), established in 2014 by the Mahatma Gandhi Vidya Peetha Trust, is a private university in Kumaraswamy Layout, South Bangalore. Its sprawling 130-acre campus offers six B.Tech programs, BBA, B.Com, B.Pharm, M.Tech, MBA, M.Pharm, and Ph.D. courses, serving over 5,000 students. DSU's modern curriculum includes Aerospace and Data Sciences, reflecting industry trends. Labs drive innovation, and placements are strong, with 85% of students placed in 2023 at firms like Amazon and Infosys, peaking at ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=DSU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2014,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"130 acres\"\n        },\n        {\n            \"id\": 25,\n            \"name\": \"Acharya Institute of Technology\",\n            \"acronym\": \"AIT\",\n            \"ranking\": 25,\n            \"address\": \"AIT, Acharya Dr. Sarvepalli Radhakrishnan Road, Soladevanahalli, Bengaluru, Karnataka - 560107\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0836\\xb0 N, 77.4819\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Automobile Engineering, Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning,  Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Mechatronics, Mining Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Science and Engineering, Digital Communication Engineering, Machine Design - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"85% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹30 LPA (2023). Balanced placements.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"120-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, dining halls.\",\n            \"labs\": \"Labs for Aeronautics, Biotech, Mechatronics, and Mining supporting research and training.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Soladevanahalli Road; Chikkabanavara Metro Station (upcoming Pink Line, ~5 km), currently reliant on buses.\",\n            \"summary\": \"Acharya Institute of Technology (AIT), established in 2000 by the Acharya Institutes Group, is a VTU-affiliated autonomous college in Soladevanahalli, North Bangalore. Its 120-acre campus offers 12 B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses, serving over 5,000 students. AIT focuses on diverse engineering fields like Aeronautical and Mining Engineering, gaining autonomy in 2017. Placements are strong, with 85% of students placed in 2023 at firms like TCS and Wipro, peaking at ₹30 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=AIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2000,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"120 acres\"\n        },\n        {\n            \"id\": 26,\n            \"name\": \"Presidency University\",\n            \"acronym\": \"PU\",\n            \"ranking\": 26,\n            \"address\": \"Presidency University, Itgalpur, Rajanakunte, Yelahanka, Bengaluru, Karnataka - 560064\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1717\\xb0 N, 77.6118\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (Artificial Intelligence & Machine Learning), Civil Engineering, Electronics and Communication Engineering, Mechanical Engineering, Petroleum Engineering, Data Science, Cybersecurity - BBA: General, Digital Marketing, Business Analytics - B.Com: General, Accounting and Taxation - B.Des: Product Design, Communication Design - B.A. LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Artificial Intelligence, Embedded Systems, Data Sciences - MBA: Finance, Marketing, Human Resources, Business Analytics - LLM: Intellectual Property Rights - **Other Programs**: Ph.D.: Engineering, Management, Law, Sciences\",\n            \"placementDetails\": \"80% placement rate; companies include Infosys, Capgemini, EY, TCS; highest package ~₹35 LPA (2023). Focus on IT and management roles.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 35,\n            \"infrastructure\": \"64-acre campus with modern classrooms, library, hostels (separate for boys and girls), sports complex (cricket, football, basketball), gym, auditorium, multi-cuisine cafeterias.\",\n            \"labs\": \"Advanced labs for AI, Civil Engineering, Electronics, Petroleum Engineering, and Cybersecurity equipped with industry-standard tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Rajanakunte Road; Yelahanka Metro Station (Green Line), ~7 km, accessible by auto or college-provided transport; upcoming Pink Line will improve connectivity.\",\n            \"summary\": \"Presidency University, established in 2013 by the Presidency Group of Institutions, is a private university located in Itgalpur, Rajanakunte, near Yelahanka in North Bangalore. Spanning a 64-acre campus, it offers a diverse range of programs including B.Tech, BBA, B.Com, B.Des, integrated law, M.Tech, MBA, LLM, and Ph.D. courses, enrolling over 5,000 students annually. The university emphasizes multidisciplinary education, blending engineering with management, design, and law. Placements are solid, with 80% of students placed in 2023 at companies like Infosys, Capgemini, and EY, with a peak package of ₹35 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PU\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2013,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"64 acres\"\n        },\n        {\n            \"id\": 27,\n            \"name\": \"Nagarjuna College of Engineering and Technology\",\n            \"acronym\": \"NCET\",\n            \"ranking\": 27,\n            \"address\": \"NCET, 38/1, Mudugurki, Devanahalli, Bengaluru, Karnataka - 562164\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.2335\\xb0 N, 77.7119\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Electronics and Communication Engineering, Artificial engineering and machine learning,  Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Construction Technology, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, Mindtree, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements with IT focus.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"100-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, surrounded by rural greenery.\",\n            \"labs\": \"Labs for Civil Engineering, CSE, ECE, and Mechanical, equipped for practical training with basic research capabilities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Devanahalli; no direct metro, ~30 km from Bangalore city center, reliant on college transport or private vehicles.\",\n            \"summary\": \"Nagarjuna College of Engineering and Technology (NCET), established in 2001 by the Nagarjuna Education Society, is a VTU-affiliated autonomous college located in Mudugurki, Devanahalli, approximately 30 km from Bangalore near the Kempegowda International Airport. Spanning an expansive 100-acre campus, NCET offers five B.E. programs, three M.Tech specializations, MBA, and Ph.D. courses, catering to around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NCET\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"100 acres\"\n        },\n        {\n            \"id\": 28,\n            \"name\": \"Sri Venkateswara College of Engineering\",\n            \"acronym\": \"SVCE\",\n            \"ranking\": 28,\n            \"address\": \"SVCE, NH-7, Vidyanagar, Kempegowda International Airport Road, Bengaluru, Karnataka - 562157\",\n            \"locationUrl\": \"https://maps.app.goo.gl/6kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1547\\xb0 N, 77.6138\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Artificial engineering and machine learning , Computer science and engineering (data science), Computer science and engineering (cyber security) ,Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Structural Engineering - **Other Programs**: MBA: General\",\n            \"placementDetails\": \"70% placement rate; companies include Infosys, HCL, Tech Mahindra, TCS; highest package ~₹20 LPA (2023). Modest placements with IT focus.\",\n            \"placementRate\": 70,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"20-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a semi-rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Civil, and Mechanical, equipped for basic practical training with limited research scope.\",\n            \"busAndMetroConvenience\": \"BMTC buses on NH-7; no direct metro, near Airport Road, ~25 km from Bangalore city center, reliant on college transport.\",\n            \"summary\": \"Sri Venkateswara College of Engineering (SVCE), established in 2001 by the Sri Venkateswara Educational and Charitable Trust, is a VTU-affiliated college located in Vidyanagar, along NH-7 near Kempegowda International Airport, about 25 km from Bangalore's core. Its 20-acre campus offers six B.E. programs, two M.Tech specializations, and an MBA, serving approximately 1,500 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SVCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"20 acres\"\n        },\n        {\n            \"id\": 29,\n            \"name\": \"SJ College of Engineering (SJCE Mysore)\",\n            \"acronym\": \"SJCE\",\n            \"ranking\": 29,\n            \"address\": \"JSS Science and Technology University (SJCE), JSS TI Campus, Manasagangothri, Mysuru, Karnataka - 570006 (Not Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.3135\\xb0 N, 76.6738\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Computer Science and Business Systems, Construction Technology and Management, Electrical and Electronics Engineering, Artificial engineering and machine learning ,Electronics and Communication Engineering, Environmental Engineering, Industrial and Production Engineering, Information Science and Engineering, Mechanical Engineering, Polymer Science and Technology - **Postgraduate (M.Tech)**: Automotive Electronics, Biotechnology, Computer Engineering, Energy Systems and Management, Environmental Engineering, Industrial Electronics, Maintenance Engineering, Software Engineering, Structural Engineering - **Other Programs**: MBA: General, MCA: General, Ph.D.: Engineering, Management, Sciences\",\n            \"placementDetails\": \"90% placement rate; companies include Infosys, Cisco, L&T, TCS; highest package ~₹40 LPA (2023). Strong core and IT placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"117-acre campus with modern classrooms, library, hostels, sports complex (cricket, basketball), gym, auditorium, and dining facilities.\",\n            \"labs\": \"Labs for Biotech, Robotics, Structural Engineering, and Polymer Science, equipped for advanced research and training.\",\n            \"busAndMetroConvenience\": \"KSRTC buses serve Mysuru; no metro in Mysuru, ~120 km from Bangalore, requiring intercity travel.\",\n            \"summary\": \"JSS Science and Technology University (SJCE), originally established as Sri Jayachamarajendra College of Engineering in 1963 by the JSS Mahavidyapeetha, is a premier autonomous institution in Mysuru, Karnataka, approximately 120 km from Bangalore. Transitioning to university status in 2016, its 117-acre campus in Manasagangothri offers 12 B.E. programs, nine M.Tech specializations, MBA, MCA, and Ph.D. courses, enrolling over 4,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCE\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1963,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"117 acres\"\n        },\n        {\n            \"id\": 30,\n            \"name\": \"SJC Institute of Technology\",\n            \"acronym\": \"SJCIT\",\n            \"ranking\": 30,\n            \"address\": \"SJCIT, P.B. No. 20, B.B. Road, Chickballapur, Karnataka - 562101 (Near Bangalore)\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.4351\\xb0 N, 77.7315\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning , Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Communication and Networking, Machine Design, Structural Engineering - **Other Programs**: MBA: General, Ph.D.: Engineering disciplines\",\n            \"placementDetails\": \"75% placement rate; companies include TCS, Wipro, HCL, Infosys; highest package ~₹25 LPA (2023). Modest mid-tier placements.\",\n            \"placementRate\": 75,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"53-acre campus with classrooms, library, hostels, sports facilities (cricket, volleyball), gym, and canteens, set in a rural environment.\",\n            \"labs\": \"Labs for CSE, ECE, Mechanical, and Civil, equipped for practical training with basic research scope.\",\n            \"busAndMetroConvenience\": \"KSRTC buses to Chickballapur; no direct metro, ~50 km from Bangalore, reliant on college transport or private vehicles.\",\n            \"summary\": \"SJC Institute of Technology (SJCIT), established in 1986 by the Sri Adichunchanagiri Shikshana Trust, is a VTU-affiliated college located in Chickballapur, about 50 km north of Bangalore. Its expansive 53-acre campus offers six B.E. programs, four M.Tech specializations, MBA, and Ph.D. courses, serving around 2,000 students.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SJCIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"Outside Top 200\",\n            \"campusSize\": \"53 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map(([range, count])=>({\n            range,\n            count\n        }));\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort(([, a], [, b])=>b - a).slice(0, 10).map(([company, mentions])=>({\n            company,\n            mentions\n        }));\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return `₹${(amount / 100).toFixed(2)} Cr`;\n    }\n    return `₹${amount} LPA`;\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return `${size} acres`;\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : `NIRF ${nirf}`;\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = (collegeName = \"\")=>{\n    const message = collegeName ? `Hi! I'm interested in learning more about ${collegeName} and would like a free consultation.` : `Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?`;\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/collegeData.js\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f884c0db3e83\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmFuZ2Fsb3JlLWVuZ2luZWVyaW5nLWNvbGxlZ2VzLy4vc3JjL3N0eWxlcy9nbG9iYWxzLmNzcz83NzlhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjg4NGMwZGIzZTgzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/compare/page.js":
/*!*********************************!*\
  !*** ./src/app/compare/page.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\education 1\src\app\compare\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Header */ \"(rsc)/./src/components/Header.js\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Footer */ \"(rsc)/./src/components/Footer.js\");\n\n\n\n\nconst metadata = {\n    title: \"Top Engineering Colleges in Bangalore | College Comparison & Rankings\",\n    description: \"Compare top engineering colleges in Bangalore. Get detailed information about placements, courses, rankings, and campus facilities. Find your perfect engineering college match.\",\n    keywords: \"engineering colleges bangalore, college comparison, placement statistics, RVCE, MSRIT, PES University, BMS College, college rankings\",\n    authors: [\n        {\n            name: \"College Comparison Platform\"\n        }\n    ],\n    openGraph: {\n        title: \"Top Engineering Colleges in Bangalore | College Comparison\",\n        description: \"Compare top engineering colleges in Bangalore with detailed placement statistics, course information, and campus facilities.\",\n        type: \"website\",\n        locale: \"en_IN\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Top Engineering Colleges in Bangalore\",\n        description: \"Compare engineering colleges in Bangalore with detailed insights.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebSite\",\n                                \"name\": \"Bangalore Engineering Colleges Comparison\",\n                                \"description\": \"Compare top engineering colleges in Bangalore with detailed placement statistics and course information\",\n                                \"url\": \"https://bangalore-engineering-colleges.com\",\n                                \"potentialAction\": {\n                                    \"@type\": \"SearchAction\",\n                                    \"target\": \"https://bangalore-engineering-colleges.com/colleges?search={search_term_string}\",\n                                    \"query-input\": \"required name=search_term_string\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gray-50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-6 right-6 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://wa.me/************?text=Hi! I need guidance on engineering colleges in Bangalore.\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-bounce-slow\",\n                            \"aria-label\": \"WhatsApp Consultation\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.js":
/*!**********************************!*\
  !*** ./src/components/Footer.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ExternalLink,Mail,MapPin,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction Footer() {\n    const quickLinks = [\n        {\n            name: \"All Colleges\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Top Ranked\",\n            href: \"/colleges?sort=ranking\"\n        },\n        {\n            name: \"Best Placements\",\n            href: \"/colleges?sort=placementRate\"\n        },\n        {\n            name: \"Compare Colleges\",\n            href: \"/compare\"\n        }\n    ];\n    const topColleges = [\n        {\n            name: \"RVCE\",\n            href: \"/colleges/1\"\n        },\n        {\n            name: \"MSRIT\",\n            href: \"/colleges/6\"\n        },\n        {\n            name: \"PES University\",\n            href: \"/colleges/4\"\n        },\n        {\n            name: \"BMS College\",\n            href: \"/colleges/5\"\n        }\n    ];\n    const resources = [\n        {\n            name: \"Placement Statistics\",\n            href: \"/colleges?sort=placementRate\"\n        },\n        {\n            name: \"Course Information\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Campus Facilities\",\n            href: \"/colleges\"\n        },\n        {\n            name: \"Admission Guide\",\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-max section-padding\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-primary-600 to-secondary-600 p-2 rounded-xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"Bangalore Engineering\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"College Comparison\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6 leading-relaxed\",\n                                    children: \"Your trusted guide to finding the perfect engineering college in Bangalore. Compare placements, courses, and facilities to make an informed decision.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Bangalore, Karnataka, India\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 62,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Top Colleges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: topColleges.map((college)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: college.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: college.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, college.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-6\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 mb-6\",\n                                    children: resources.map((resource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: resource.href,\n                                                className: \"text-gray-400 hover:text-white transition-colors duration-200 flex items-center group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: resource.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, resource.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+91 98765 43210\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ExternalLink_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"\\xa9 2024 Bangalore Engineering College Comparison. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"hover:text-white transition-colors duration-200\",\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\education 1\\\\src\\\\components\\\\Footer.js\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.js\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\education 1\src\components\Header.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcompare%2Fpage&page=%2Fcompare%2Fpage&appPaths=%2Fcompare%2Fpage&pagePath=private-next-app-dir%2Fcompare%2Fpage.js&appDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cuser%5CDocuments%5Caugment-projects%5Ceducation%201&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();