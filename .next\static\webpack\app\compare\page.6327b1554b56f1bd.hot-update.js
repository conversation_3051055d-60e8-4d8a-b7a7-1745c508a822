"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/compare/page",{

/***/ "(app-pages-browser)/./src/lib/collegeData.js":
/*!********************************!*\
  !*** ./src/lib/collegeData.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colleges: function() { return /* binding */ colleges; },\n/* harmony export */   filterColleges: function() { return /* binding */ filterColleges; },\n/* harmony export */   formatCampusSize: function() { return /* binding */ formatCampusSize; },\n/* harmony export */   formatCurrency: function() { return /* binding */ formatCurrency; },\n/* harmony export */   formatNIRF: function() { return /* binding */ formatNIRF; },\n/* harmony export */   getAggregateStats: function() { return /* binding */ getAggregateStats; },\n/* harmony export */   getAllColleges: function() { return /* binding */ getAllColleges; },\n/* harmony export */   getCollegeById: function() { return /* binding */ getCollegeById; },\n/* harmony export */   getFeaturedColleges: function() { return /* binding */ getFeaturedColleges; },\n/* harmony export */   getPlacementStats: function() { return /* binding */ getPlacementStats; },\n/* harmony export */   getTopCompanies: function() { return /* binding */ getTopCompanies; },\n/* harmony export */   getWhatsAppLink: function() { return /* binding */ getWhatsAppLink; },\n/* harmony export */   searchColleges: function() { return /* binding */ searchColleges; },\n/* harmony export */   sortColleges: function() { return /* binding */ sortColleges; }\n/* harmony export */ });\n// College data processing utilities\n// College data - using a simpler approach to avoid import issues\nlet collegesData = null;\n// Load data function\nconst loadCollegeData = async ()=>{\n    if ( true && !collegesData) {\n        try {\n            const response = await fetch(\"/colleges.json\");\n            collegesData = await response.json();\n        } catch (error) {\n            console.error(\"Failed to load college data:\", error);\n            collegesData = getFallbackData();\n        }\n    }\n    return collegesData || getFallbackData();\n};\n// Fallback data for SSR and error cases - Complete dataset from college.json\nconst getFallbackData = ()=>[\n        {\n            \"id\": 1,\n            \"name\": \"Rashtreeya Vidyalaya College of Engineering\",\n            \"acronym\": \"RVCE\",\n            \"ranking\": 1,\n            \"address\": \"Rashtreeya Vidyalaya College of Engineering, Mysuru Road, R.V. Vidyaniketan Post, Bengaluru, Karnataka - 560059, India.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aerospace Engineering, Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Computer Science and Engineering (Data Science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Bio-Medical Signal Processing & Instrumentation, Biotechnology, Chemical Engineering, Communication Systems, Computer Integrated Manufacturing, Computer Network Engineering, Computer Science and Engineering, Digital Communication, Highway Technology, Information Technology, Machine Design, Power Electronics, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: Master of Computer Applications (MCA), Doctoral Programs (Ph.D.) in all engineering departments, Biotechnology, Chemistry, Physics, Mathematics, and Management Studies.\",\n            \"placementDetails\": \"Over 1,400 offers for UG and 430 for PG students in 2022. Companies include Microsoft, Goldman Sachs, Cisco, Citrix, Soroco, Fivetran, Clumio, and 281+ firms for UG, 116+ for PG. Highest packages: ₹53.18 LPA (domestic), ₹1.15 crore (international). Known for tech and core engineering placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 53,\n            \"infrastructure\": \"16.85-acre campus with a green, sylvan setting. Features modern classrooms, seminar halls, a central library, hostels (separate for boys and girls), sports complex (indoor and outdoor), gym, medical center, Wi-Fi, and multiple food courts.\",\n            \"labs\": \"State-of-the-art facilities including Robotics Lab, VLSI Design Lab, Aerospace Engineering Lab, Biotechnology Research Lab, Embedded Systems Lab, and advanced computing labs with industry-grade equipment.\",\n            \"busAndMetroConvenience\": \"Well-connected via BMTC buses along Mysuru Road, with stops near R.V. Vidyaniketan Post. Closest metro: Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km, accessible by auto or feeder transport. Upcoming Yellow Line (expected April 2025) will enhance connectivity.\",\n            \"summary\": \"Rashtreeya Vidyalaya College of Engineering (RVCE), established in 1963, stands as one of India's premier autonomous engineering institutions under Visvesvaraya Technological University (VTU). Managed by the Rashtreeya Sikshana Samithi Trust (RSST), RVCE began with three branches—Civil, Mechanical, and Electrical—and has since expanded to offer 15 undergraduate and 14 postgraduate engineering programs, alongside MCA and Ph.D. courses. Located 13 km from central Bangalore on Mysuru Road, its 16.85-acre campus blends natural beauty with cutting-edge infrastructure, fostering an environment conducive to academic and extracurricular growth. RVCE is consistently ranked among India's top engineering colleges, securing 99th place in the NIRF 2024 Engineering rankings. Its academic excellence is complemented by a robust placement record, attracting global giants like Microsoft and Goldman Sachs, with packages reaching ₹1.15 crore internationally in 2022. The college's research focus is evident in its numerous patents, funded projects, and collaborations with organizations like ISRO and DRDO. The campus hosts advanced labs, such as the Aerospace Engineering Lab equipped for satellite design and the Biotechnology Lab supporting groundbreaking research. Students benefit from a vibrant campus life, with over 50 clubs (technical and cultural), annual fests like 8th Mile, and sports facilities including a cricket ground and gymnasium. RVCE's alumni network is illustrious, featuring figures like Anil Kumble and Chetan Baboor, reflecting its legacy of producing leaders. Connectivity is a strength, with the Green Line metro station nearby and BMTC buses ensuring easy access, soon to be enhanced by the Yellow Line. With a student intake exceeding 1,400 annually, RVCE balances tradition with innovation, making it a top choice for engineering aspirants in Karnataka and beyond.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1963,\n            \"nirf\": \"99\",\n            \"campusSize\": \"16.85 acres\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"RV Institute of Technology and Management\",\n            \"acronym\": \"RVITM\",\n            \"ranking\": 2,\n            \"address\": \"RV Institute of Technology and Management, No. 312/3, Sy. No. CA 8, 9th Main Road, Kothanur Dinne Main Road, JP Nagar 8th Phase, Bengaluru, Karnataka - 560076.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3Xz5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8731\\xb0 N, 77.5907\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Computer Science and Engineering, Computer Science and Engineering (Artificial Intelligence and Machine Learning), Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (Planned/Under Development)**: M.Tech in Computer Science and Engineering (proposed), M.Tech in VLSI Design and Embedded Systems (proposed) - **Other Programs**: Ph.D.: Engineering disciplines (under consideration as per RVITM's growth plans).\",\n            \"placementDetails\": \"Strong industry ties with companies like Infosys, Wipro, TCS, and Capgemini. Highest package ~₹20 LPA (2023). Placement strength expected to grow with time as the institute matures.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 20,\n            \"infrastructure\": \"5-acre campus in JP Nagar with smart classrooms, seminar halls, a central library, hostels, sports complex (indoor and outdoor), gymnasium, Wi-Fi, and a cafeteria offering diverse cuisines.\",\n            \"labs\": \"Advanced facilities including Artificial Intelligence Lab, Robotics Lab, Electronics and Embedded Systems Lab, Mechanical Workshop, and high-performance computing labs with industry-standard software.\",\n            \"busAndMetroConvenience\": \"BMTC buses on JP Nagar routes; closest metro is JP Nagar Station (Green Line), ~4 km, easily reachable by auto or bus. The upcoming Yellow Line (Electronic City extension) will further improve access.\",\n            \"summary\": \"RVITM, established in 2002 by RSST, is a growing institution offering B.E., proposed M.Tech programs, and Ph.D. options. Located in JP Nagar, its compact campus features modern infrastructure and labs. Strong placements with a peak package of ₹20 LPA in 2023, though still developing compared to peers.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVITM\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2002,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 3,\n            \"name\": \"RV University\",\n            \"acronym\": \"RVU\",\n            \"ranking\": 3,\n            \"address\": \"RV University, RV Vidyaniketan Post, 8th Mile, Mysuru Road, Bengaluru, Karnataka - 560059.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/8kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9237\\xb0 N, 77.4987\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, CSE (AI & ML), CSE (Data Science), Electronics and Communication Engineering; B.Sc. (Hons): Physics, Chemistry, Mathematics, Computer Science, Environmental Science; B.A. (Hons): Economics, Sociology, Political Science, Psychology, Journalism; BBA (Hons): General, Entrepreneurship; B.Com (Hons): General, Banking and Finance; B.Des: Product Design, User Experience Design; LL.B (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: Data Science, VLSI Design, Artificial Intelligence; M.Des: Product Design, Interaction Design; M.A.: Economics, Journalism and Communication; MBA: Business Analytics, Marketing, Finance - **Other Programs**: Ph.D.: Engineering, Liberal Arts, Design, Management, Sciences.\",\n            \"placementDetails\": \"Early batches report 85% placement with companies like Deloitte, KPMG, Amazon, Infosys; highest package ~₹25 LPA (2023). Placement strength expected to grow with time.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 25,\n            \"infrastructure\": \"50-acre shared campus with RVCE, featuring smart classrooms, design studios, library, hostels (boys and girls), sports complex (cricket, football), amphitheater, and cafeterias.\",\n            \"labs\": \"Specialized labs for AI, IoT, VLSI Design, Physics, Chemistry, and prototyping studios for design students.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Mysuru Road; Rashtreeya Vidyalaya Road Metro Station (Green Line), ~2-3 km. Upcoming Yellow Line (April 2025) will improve access.\",\n            \"summary\": \"RV University, established in 2021 by RSST, blends technical education with liberal arts and design. Located on Mysuru Road, it offers diverse programs across six schools. Early graduates secured roles at firms like Deloitte and Amazon, peaking at ₹25 LPA in 2023. The campus boasts modern infrastructure and specialized labs, while connectivity includes BMTC buses and the nearby Green Line metro station.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=RVU\",\n            \"metroAccess\": true,\n            \"establishedYear\": 2021,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"50 acres\"\n        },\n        {\n            \"id\": 4,\n            \"name\": \"PES University (Ring Road Campus)\",\n            \"acronym\": \"PESURRC\",\n            \"ranking\": 4,\n            \"address\": \"PES University, 100 Feet Ring Road, BSK III Stage, Bengaluru, Karnataka - 560085.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/5kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9345\\xb0 N, 77.5345\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate**: B.Tech: Computer Science and Engineering, Electronics and Communication Engineering, CSE (AI & ML),Mechanical Engineering, Electrical and Electronics Engineering, Biotechnology; BBA: General, Hospitality and Event Management; BCA: General; B.Arch: Architecture; B.Des: Product Design, Interaction Design, Communication Design; BBA-LLB (Hons): 5-year integrated law program - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Thermal Engineering), EE (Power Electronics), Biotech (Bioinformatics); MBA: Finance, Marketing, HR, Business Analytics; MCA: General; M.Com: General - **Other Programs**: Ph.D.: Engineering, Management, Sciences, Architecture.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Microsoft, Google, IBM, Accenture; highest package ~₹65 LPA (2023). Strong tech and consulting recruitment.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 65,\n            \"infrastructure\": \"25-acre campus with advanced lecture halls, central library, hostels, sports arena (basketball, football), gym, medical center, and dining options.\",\n            \"labs\": \"Cutting-edge facilities for Robotics, Embedded Systems, Biotechnology, Civil Engineering, and high-performance computing labs.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Ring Road; Banashankari Metro Station (Green Line), ~2 km, easily reachable by auto or walk.\",\n            \"summary\": \"PES University, founded in 1972, offers a blend of technical, management, and design education. Ranked 101-150 in NIRF 2024 (University), it emphasizes practical skills and innovation. The campus features modern infrastructure and advanced labs, with strong placements at firms like Microsoft and Google, peaking at ₹65 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESURRC\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1972,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 5,\n            \"name\": \"BMS College of Engineering\",\n            \"acronym\": \"BMSCE\",\n            \"ranking\": 5,\n            \"address\": \"BMS College of Engineering, Bull Temple Road, Basavanagudi, Bengaluru, Karnataka - 560019.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/7kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9417\\xb0 N, 77.5659\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(Internet of things and cyber security including block chain), Computer Science and Engineering (Business system) , Mechanical Engineering, Medical Electronics - **Postgraduate (M.Tech)**: Biochemical Engineering, Computer Science and Engineering, Construction Technology, Digital Communication, Environmental Engineering, Machine Design, Manufacturing Science, Power Electronics, Thermal Engineering, Transportation Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: All engineering disciplines, Management, Sciences.\",\n            \"placementDetails\": \"85%+ placement rate; companies include TCS, Infosys, Bosch, Accenture; highest package ~₹45 LPA (2023). Strong in core and IT sectors.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 45,\n            \"infrastructure\": \"11-acre urban campus with modern classrooms, library, hostels, auditorium, sports facilities (cricket, basketball), gym, and food courts.\",\n            \"labs\": \"Well-equipped labs for Aerospace, Biotech, VLSI, Mechanical, and Civil Engineering, supporting research and practical training.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent Bull Temple Road; National College Metro Station (Green Line), ~1 km, highly accessible.\",\n            \"summary\": \"BMSCE, established in 1946, is India's first private engineering college. Located in Basavanagudi, its 11-acre campus combines historical significance with modern facilities. Affiliated with VTU and autonomous since 2008, it offers 12 B.E. programs and robust placements at firms like TCS and Bosch, peaking at ₹45 LPA in 2023.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BMSCE\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1946,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"11 acres\"\n        },\n        {\n            \"id\": 6,\n            \"name\": \"MS Ramaiah Institute of Technology\",\n            \"acronym\": \"MSRIT\",\n            \"ranking\": 6,\n            \"address\": \"MS Ramaiah Institute of Technology, MSR Nagar, MSRIT Post, Bengaluru, Karnataka - 560054.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/9kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.0306\\xb0 N, 77.5653\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Chemical Engineering, Civil Engineering, Computer Science and Engineering, , Artificial Intelligence and machine learning, Artificial intelligence and data science, Computer Science and Engineering(data science), Computer Science and Engineering(cyber security), Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering, Medical Electronics, Telecommunication Engineering - **Postgraduate (M.Tech)**: Biotechnology, Computer Integrated Manufacturing, Computer Science and Engineering, Digital Communication, Digital Electronics and Communication, Industrial Engineering, Manufacturing Science and Engineering, Software Engineering, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: B.Arch: Architecture; MBA: General; MCA: General; Ph.D.: All engineering disciplines, Architecture, Management.\",\n            \"placementDetails\": \"95% placement rate; companies include Amazon, Capgemini, Intel, TCS; highest package ~₹50 LPA (2023). Excellent tech and core placements.\",\n            \"placementRate\": 95,\n            \"highestPackage\": 50,\n            \"infrastructure\": \"25-acre campus with smart classrooms, central library, hostels, sports complex (cricket, volleyball), gym, auditorium, and dining halls.\",\n            \"labs\": \"Advanced labs for AI, VLSI, Structural Engineering, Biotech, and Mechanical Engineering, equipped for research and industry projects.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve MSR Nagar; Sandal Soap Factory Metro Station (Green Line), ~2 km, accessible by auto or walk.\",\n            \"summary\": \"MSRIT, founded in 1962 by Dr. M.S. Ramaiah, spans 25 acres in North Bangalore. It offers 12 B.E. programs, 12 M.Tech specializations, and strong placements at firms like Amazon and Intel, peaking at ₹50 LPA in 2023. Labs support cutting-edge research, and connectivity includes BMTC buses and the Green Line metro.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=MSRIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1962,\n            \"nirf\": \"78\",\n            \"campusSize\": \"25 acres\"\n        },\n        {\n            \"id\": 7,\n            \"name\": \"Sir M Visvesvaraya Institute of Technology\",\n            \"acronym\": \"Sir MVIT\",\n            \"ranking\": 7,\n            \"address\": \"Sir MVIT, Krishnadevaraya Nagar, Hunasamaranahalli, International Airport Road, Bengaluru, Karnataka - 562157.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/1kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1507\\xb0 N, 77.6082\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Biotechnology, Civil Engineering, Computer Science and Engineering, Artificial engineering and machine learning, Computer science and engineering (cyber security and IoT) Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering, Telecommunication Engineering - **Postgraduate (M.Tech)**: Computer Integrated Manufacturing, Electronics, Mechanical Engineering (Design) - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines, Management.\",\n            \"placementDetails\": \"80%+ placement rate; companies include TCS, Wipro, Cognizant, Infosys; highest package ~₹30 LPA (2023). Solid mid-tier placements.\",\n            \"placementRate\": 80,\n            \"highestPackage\": 30,\n            \"infrastructure\": \"133-acre campus with classrooms, library, hostels, sports facilities (cricket, basketball), gym, auditorium, and canteens.\",\n            \"labs\": \"Labs for Electronics, Mechanical, Biotech, and Computer Science, supporting practical and research activities.\",\n            \"busAndMetroConvenience\": \"BMTC buses to Airport Road; no direct metro, ~20 km from Yelahanka Metro Station (Green Line), requiring private transport.\",\n            \"summary\": \"Sir MVIT, established in 1986, spans a vast 133-acre campus on International Airport Road. It offers eight B.E. programs and reliable placements at firms like TCS and Wipro, peaking at ₹30 LPA in 2023. While its rural location limits accessibility, the large campus provides ample space for expansion.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=SirMVIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 1986,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"133 acres\"\n        },\n        {\n            \"id\": 8,\n            \"name\": \"Bangalore Institute of Technology\",\n            \"acronym\": \"BIT\",\n            \"ranking\": 8,\n            \"address\": \"BIT, K.R. Road, V.V. Puram, Bengaluru, Karnataka - 560004.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/2kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.9561\\xb0 N, 77.5762\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Artificial Intelligence and Machine Learning, Civil Engineering, Computer Science and Engineering, Electrical and Electronics Engineering, Electronics and Communication Engineering, Electronics and Instrumentation Engineering, Industrial Engineering and Management, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Digital Electronics and Communication, Machine Design, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering disciplines.\",\n            \"placementDetails\": \"85% placement rate; companies include Infosys, Dell, Accenture, TCS; highest package ~₹37 LPA (2023). Strong IT and core engineering focus.\",\n            \"placementRate\": 85,\n            \"highestPackage\": 37,\n            \"infrastructure\": \"5-acre urban campus with classrooms, library, hostels, sports area (volleyball, badminton), auditorium, and canteens.\",\n            \"labs\": \"Labs for AI, VLSI, Civil, Mechanical, and Electronics, equipped for practical training and small-scale research.\",\n            \"busAndMetroConvenience\": \"BMTC buses frequent K.R. Road; Chickpet Metro Station (Green Line), ~1.5 km, highly accessible.\",\n            \"summary\": \"BIT, founded in 1979 under the Vokkaligara Sangha, is a well-regarded VTU-affiliated college in central Bangalore. Its compact 5-acre campus hosts nine B.E. programs, five M.Tech specializations, MBA, MCA, and Ph.D. courses. Placements are strong, with 85% of students placed in 2023 at firms like Infosys and Dell, peaking at ₹37 LPA.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=BIT\",\n            \"metroAccess\": true,\n            \"establishedYear\": 1979,\n            \"nirf\": \"N/A\",\n            \"campusSize\": \"5 acres\"\n        },\n        {\n            \"id\": 9,\n            \"name\": \"Nitte Meenakshi Institute of Technology\",\n            \"acronym\": \"NMIT\",\n            \"ranking\": 9,\n            \"address\": \"NMIT, P.B. No. 6429, Yelahanka, Bengaluru, Karnataka - 560064.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/3kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"13.1276\\xb0 N, 77.5869\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.E.)**: Aeronautical Engineering, Civil Engineering, Artificial engineering and machine learning, Artificial learning and data science, Computer Science and Engineering, Computer Science and Engineering (Business system), Electrical and Electronics Engineering, Electronics and Communication Engineering, Information Science and Engineering, Mechanical Engineering - **Postgraduate (M.Tech)**: Computer Science and Engineering, Data Sciences, Machine Design, Renewable Energy, Structural Engineering, VLSI Design and Embedded Systems - **Other Programs**: MBA: General; MCA: General; Ph.D.: Engineering, Management, Sciences.\",\n            \"placementDetails\": \"90% placement rate; companies include Microsoft, Infosys, Huawei, TCS; highest package ~₹40 LPA (2023). Strong tech focus.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 40,\n            \"infrastructure\": \"23-acre campus with modern classrooms, library, hostels, sports facilities (cricket, tennis), gym, and dining halls.\",\n            \"labs\": \"Robotics Lab, Aerospace Lab, Data Science Lab, VLSI Lab, and Mechanical Workshop, supporting advanced research.\",\n            \"busAndMetroConvenience\": \"BMTC buses serve Yelahanka; Yelahanka Metro Station (Green Line), ~5 km, accessible by auto or bus.\",\n            \"summary\": \"NMIT, established in 2001 by the Nitte Education Trust, is an autonomous VTU-affiliated college in Yelahanka. Spanning 23 acres, it offers seven B.E. programs, six M.Tech specializations, MBA, MCA, and Ph.D. courses. Ranked 151-200 in NIRF 2024 (Engineering), it emphasizes innovation and research, with over 50 patents filed.\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=NMIT\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2001,\n            \"nirf\": \"151-200\",\n            \"campusSize\": \"23 acres\"\n        },\n        {\n            \"id\": 10,\n            \"name\": \"PES University (Electronic City Campus)\",\n            \"acronym\": \"PESUECC\",\n            \"ranking\": 10,\n            \"address\": \"PES University, Electronic City Campus, Hosur Road, Electronic City, Bengaluru, Karnataka - 560100.\",\n            \"locationUrl\": \"https://maps.app.goo.gl/4kW5z5v5X5J5Z5v5\",\n            \"coordinates\": \"12.8406\\xb0 N, 77.6635\\xb0 E\",\n            \"coursesOffered\": \"- **Undergraduate (B.Tech)**: Computer Science and Engineering, Artificial engineering and machine learning,  Electronics and Communication Engineering, Mechanical Engineering - **Postgraduate**: M.Tech: CSE (AI & ML), ECE (VLSI Design), Mechanical (Automotive Engineering); MBA: Finance, Marketing, HR; MCA: General - **Other Programs**: Ph.D.: Engineering, Management.\",\n            \"placementDetails\": \"90%+ placement rate; companies include Amazon, Intel, Flipkart, TCS; highest package ~₹60 LPA (2023). Excellent tech placements.\",\n            \"placementRate\": 90,\n            \"highestPackage\": 60,\n            \"infrastructure\": \"50-acre campus with advanced classrooms, library, hostels, sports complex (football, basketball), gym, and cafeterias.\",\n            \"labs\": \"Labs for AI, Electronics, Automotive Engineering, and Software Development, equipped with industry-grade tools.\",\n            \"busAndMetroConvenience\": \"BMTC buses on Hosur Road; Electronic City Metro Station (upcoming Yellow Line, ~2 km), currently reliant on buses or autos.\",\n            \"summary\": \"PES University's Electronic City Campus, established in 2005, offers three B.Tech programs, three M.Tech specializations, MBA, MCA, and Ph.D. courses. Proximity to Electronic City enhances tech exposure. Ranked alongside its Ring Road counterpart in NIRF 2024 (101-150, University), this campus benefits from PES's legacy and alumni like Nishanth Ananthram (Google).\",\n            \"image\": \"/placeholder.svg?height=400&width=600&text=PESUECC\",\n            \"metroAccess\": false,\n            \"establishedYear\": 2005,\n            \"nirf\": \"101-150\",\n            \"campusSize\": \"50 acres\"\n        }\n    ];\nconst colleges = getFallbackData();\n// Get all colleges\nconst getAllColleges = ()=>{\n    return colleges;\n};\n// Get college by ID\nconst getCollegeById = (id)=>{\n    return colleges.find((college)=>college.id === parseInt(id));\n};\n// Get featured colleges (top 6 by ranking)\nconst getFeaturedColleges = ()=>{\n    return colleges.sort((a, b)=>a.ranking - b.ranking).slice(0, 6);\n};\n// Search colleges by name or acronym\nconst searchColleges = (query)=>{\n    if (!query) return colleges;\n    const searchTerm = query.toLowerCase();\n    return colleges.filter((college)=>college.name.toLowerCase().includes(searchTerm) || college.acronym.toLowerCase().includes(searchTerm) || college.coursesOffered.toLowerCase().includes(searchTerm));\n};\n// Filter colleges by various criteria\nconst filterColleges = (filters)=>{\n    let filteredColleges = [\n        ...colleges\n    ];\n    // Filter by placement rate\n    if (filters.minPlacementRate) {\n        filteredColleges = filteredColleges.filter((college)=>college.placementRate >= filters.minPlacementRate);\n    }\n    // Filter by highest package\n    if (filters.minPackage) {\n        filteredColleges = filteredColleges.filter((college)=>college.highestPackage >= filters.minPackage);\n    }\n    // Filter by metro access\n    if (filters.metroAccess !== undefined) {\n        filteredColleges = filteredColleges.filter((college)=>college.metroAccess === filters.metroAccess);\n    }\n    // Filter by establishment year range\n    if (filters.establishedAfter) {\n        filteredColleges = filteredColleges.filter((college)=>college.establishedYear >= filters.establishedAfter);\n    }\n    // Filter by campus size\n    if (filters.minCampusSize) {\n        filteredColleges = filteredColleges.filter((college)=>{\n            const campusSize = parseFloat(college.campusSize);\n            return campusSize >= filters.minCampusSize;\n        });\n    }\n    // Filter by courses (basic text search in coursesOffered)\n    if (filters.course) {\n        const courseSearch = filters.course.toLowerCase();\n        filteredColleges = filteredColleges.filter((college)=>college.coursesOffered.toLowerCase().includes(courseSearch));\n    }\n    return filteredColleges;\n};\n// Sort colleges by various criteria\nconst sortColleges = (colleges, sortBy)=>{\n    const sortedColleges = [\n        ...colleges\n    ];\n    switch(sortBy){\n        case \"ranking\":\n            return sortedColleges.sort((a, b)=>a.ranking - b.ranking);\n        case \"placementRate\":\n            return sortedColleges.sort((a, b)=>b.placementRate - a.placementRate);\n        case \"highestPackage\":\n            return sortedColleges.sort((a, b)=>b.highestPackage - a.highestPackage);\n        case \"establishedYear\":\n            return sortedColleges.sort((a, b)=>a.establishedYear - b.establishedYear);\n        case \"campusSize\":\n            return sortedColleges.sort((a, b)=>{\n                const sizeA = parseFloat(a.campusSize);\n                const sizeB = parseFloat(b.campusSize);\n                return sizeB - sizeA;\n            });\n        case \"name\":\n            return sortedColleges.sort((a, b)=>a.name.localeCompare(b.name));\n        default:\n            return sortedColleges;\n    }\n};\n// Get aggregate statistics\nconst getAggregateStats = ()=>{\n    const totalColleges = colleges.length;\n    const avgPlacementRate = Math.round(colleges.reduce((sum, college)=>sum + college.placementRate, 0) / totalColleges);\n    const highestPackageOverall = Math.max(...colleges.map((college)=>college.highestPackage));\n    const avgCampusSize = (colleges.reduce((sum, college)=>sum + parseFloat(college.campusSize), 0) / totalColleges).toFixed(2);\n    const metroAccessibleCount = colleges.filter((college)=>college.metroAccess).length;\n    return {\n        totalColleges,\n        avgPlacementRate,\n        highestPackageOverall,\n        avgCampusSize,\n        metroAccessibleCount,\n        studentsGuided: \"1000+\"\n    };\n};\n// Get placement statistics for charts\nconst getPlacementStats = ()=>{\n    const packageRanges = {\n        \"0-10 LPA\": 0,\n        \"10-20 LPA\": 0,\n        \"20-30 LPA\": 0,\n        \"30-40 LPA\": 0,\n        \"40-50 LPA\": 0,\n        \"50+ LPA\": 0\n    };\n    colleges.forEach((college)=>{\n        const pkg = college.highestPackage;\n        if (pkg <= 10) packageRanges[\"0-10 LPA\"]++;\n        else if (pkg <= 20) packageRanges[\"10-20 LPA\"]++;\n        else if (pkg <= 30) packageRanges[\"20-30 LPA\"]++;\n        else if (pkg <= 40) packageRanges[\"30-40 LPA\"]++;\n        else if (pkg <= 50) packageRanges[\"40-50 LPA\"]++;\n        else packageRanges[\"50+ LPA\"]++;\n    });\n    return Object.entries(packageRanges).map((param)=>{\n        let [range, count] = param;\n        return {\n            range,\n            count\n        };\n    });\n};\n// Get top companies from placement details\nconst getTopCompanies = ()=>{\n    const companyMentions = {};\n    const commonCompanies = [\n        \"Microsoft\",\n        \"Google\",\n        \"Amazon\",\n        \"TCS\",\n        \"Infosys\",\n        \"Wipro\",\n        \"Accenture\",\n        \"IBM\",\n        \"Cisco\",\n        \"Intel\",\n        \"Goldman Sachs\",\n        \"Deloitte\"\n    ];\n    colleges.forEach((college)=>{\n        const placementText = college.placementDetails.toLowerCase();\n        commonCompanies.forEach((company)=>{\n            if (placementText.includes(company.toLowerCase())) {\n                companyMentions[company] = (companyMentions[company] || 0) + 1;\n            }\n        });\n    });\n    return Object.entries(companyMentions).sort((param, param1)=>{\n        let [, a] = param, [, b] = param1;\n        return b - a;\n    }).slice(0, 10).map((param)=>{\n        let [company, mentions] = param;\n        return {\n            company,\n            mentions\n        };\n    });\n};\n// Format currency\nconst formatCurrency = (amount)=>{\n    if (amount >= 100) {\n        return \"₹\".concat((amount / 100).toFixed(2), \" Cr\");\n    }\n    return \"₹\".concat(amount, \" LPA\");\n};\n// Format campus size\nconst formatCampusSize = (size)=>{\n    return \"\".concat(size, \" acres\");\n};\n// Get NIRF ranking display\nconst formatNIRF = (nirf)=>{\n    return nirf === \"N/A\" ? \"Not Ranked\" : \"NIRF \".concat(nirf);\n};\n// Generate WhatsApp consultation link\nconst getWhatsAppLink = function() {\n    let collegeName = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"\";\n    const message = collegeName ? \"Hi! I'm interested in learning more about \".concat(collegeName, \" and would like a free consultation.\") : \"Hi! I'm looking for guidance on engineering colleges in Bangalore. Can you help me with a free consultation?\";\n    const phoneNumber = \"************\"; // Replace with actual WhatsApp number\n    return \"https://wa.me/\".concat(phoneNumber, \"?text=\").concat(encodeURIComponent(message));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/collegeData.js\n"));

/***/ })

});